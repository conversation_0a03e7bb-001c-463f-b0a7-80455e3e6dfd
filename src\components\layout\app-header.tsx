// src/components/layout/app-header.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useAuth } from '../auth/auth-provider';

interface AppHeaderProps {
  title: string;
  currentRoute?: string;
}

const AppHeader = ({ title, currentRoute = 'dashboard' }: AppHeaderProps) => {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const router = useRouter();
  const { user, signOut } = useAuth();

  const handleProfileNavigation = () => {
    router.push('/settings/profile'); // Navigate to profile settings page
    setIsProfileOpen(false);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      setIsProfileOpen(false);
      router.push('/product');
    } catch (error) {
      console.error('Sign out error:', error);
      // Force redirect even if sign out fails
      router.push('/product');
    }
  };

  const getBreadcrumb = () => {
    const routeMap: Record<string, string> = {
      'dashboard': 'Dashboard',
      'student-management': 'Student Management',
    };

    const routeName = routeMap[currentRoute] || 'Dashboard';
    
    return (
      <nav className="header-breadcrumb hidden md:flex items-center space-x-2 text-sm-app">
        <span>EduPro</span>
        <span>/</span>
        <span className="header-breadcrumb-current">{routeName}</span>
        {title !== routeName && (
          <>
            <span>/</span>
            <span className="header-breadcrumb-current">{title}</span>
          </>
        )}
      </nav>
    );
  };

  return (
    <header className="header-dark header-height shadow-sm border-b px-4 py-2">
      <div className="flex items-center justify-between h-full">
        {/* Title and Breadcrumb */}
        <div className="flex items-center space-x-4">
          <h1 className="header-title">{title}</h1>
          {getBreadcrumb()}
        </div>

        {/* Header Actions */}
        <div className="flex items-center space-x-4">
          {/* Profile Dropdown */}
          <div className="relative">
            <button
              onClick={() => setIsProfileOpen(!isProfileOpen)}
              className="header-action-btn flex items-center space-x-3 p-2 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm-app font-medium">U</span>
              </div>
              <div className="hidden md:block text-left">
                <p className="header-profile-name text-sm-app font-medium">{user?.name || 'User'}</p>
                <p className="header-profile-role text-xs-app">{user?.role || 'User'}</p>
              </div>
              <svg className="h-4 w-4 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>

            {/* Profile Dropdown Menu */}
            {isProfileOpen && (
              <div className="header-profile-dropdown absolute right-0 mt-2 w-48 rounded-lg shadow-lg border py-1 z-50">
                <button
                  onClick={handleProfileNavigation}
                  className="header-dropdown-item block w-full text-left px-4 py-2 text-sm-app"
                >
                  Profile Settings
                </button>
                <button
                  onClick={handleProfileNavigation}
                  className="header-dropdown-item block w-full text-left px-4 py-2 text-sm-app"
                >
                  Account Settings
                </button>
                <hr className="my-1 border-slate-600" />
                <button
                  onClick={handleSignOut}
                  className="header-dropdown-item danger block w-full text-left px-4 py-2 text-sm-app"
                >
                  Sign Out
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default AppHeader;
