// src/components/landing/landing-footer.tsx
'use client';

const LandingFooter = () => {
  const currentYear = new Date().getFullYear();
  return (
    <footer className="bg-slate-900 text-white py-8 font-sans">
      <div className="container mx-auto px-4">
        {/* Main Footer Content */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
          {/* Brand Section */}
          <div className="flex items-center space-x-3">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-indigo-400"
            >
              <path
                d="M22 9L12 14L2 9L12 4L22 9ZM12 15.5L6 12.5V16.5L12 19.5L18 16.5V12.5L12 15.5Z"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M4 9.5V15L12 19.5L20 15V9.5L12 14L4 9.5Z"
                fill="currentColor"
                fillOpacity="0.3"
              />
            </svg>            <div>              <h3 className="text-xl font-bold">EduPro</h3>
              <p className="text-slate-400 text-sm">School Management Solutions</p>
            </div>
          </div>

          {/* Quick Navigation */}
          <div className="flex flex-wrap gap-6 text-base">
            <a href="/product" className="text-slate-400 hover:text-indigo-400 transition-colors">Product</a>
            <a href="/resources" className="text-slate-400 hover:text-indigo-400 transition-colors">Resources</a>
            <a href="#help" className="text-slate-400 hover:text-indigo-400 transition-colors">Help</a>
            <a href="#privacy" className="text-slate-400 hover:text-indigo-400 transition-colors">Privacy</a>
          </div>

          {/* Social Links & CTA */}
          <div className="flex items-center space-x-4">
            <div className="flex space-x-3">
              <a href="#twitter" className="text-slate-400 hover:text-indigo-400 transition-colors" aria-label="Twitter">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                </svg>
              </a>
              <a href="#linkedin" className="text-slate-400 hover:text-indigo-400 transition-colors" aria-label="LinkedIn">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>
        </div>        {/* Bottom Bar */}
        <div className="border-t border-slate-800 mt-6 pt-4">
          <div className="text-center text-sm text-slate-400">
            © {currentYear} EduPro. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};

export default LandingFooter;
