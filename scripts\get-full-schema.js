#!/usr/bin/env node

/**
 * Comprehensive script to get full table schema from Supabase
 * Uses multiple approaches to get complete column information
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Known tables from our discovery
const knownTables = [
  'profiles', 'students', 'classes', 'sections', 'academic_years',
  'guardian_relations', 'guardians', 'documents', 'academic_records',
  'enrollments', 'teachers', 'subjects', 'attendance', 'grades'
];

async function getTableStructure(tableName) {
  try {
    console.log(`🔍 Analyzing table: ${tableName}`);
    
    // Method 1: Try to get sample data
    const { data: sampleData, error: sampleError } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    if (!sampleError && sampleData && sampleData.length > 0) {
      const columns = Object.keys(sampleData[0]).map(key => ({
        name: key,
        type: inferType(sampleData[0][key]),
        nullable: true,
        default: null
      }));
      return { columns, hasData: true };
    }

    // Method 2: Try to insert and rollback to discover structure
    // This is a bit hacky but can work for discovering required fields
    try {
      const { error: insertError } = await supabase
        .from(tableName)
        .insert({});

      if (insertError && insertError.message) {
        // Parse error message to find required columns
        const columns = parseErrorForColumns(insertError.message, tableName);
        if (columns.length > 0) {
          return { columns, hasData: false };
        }
      }
    } catch (e) {
      // Ignore insert errors
    }

    // Method 3: Use common patterns based on table name
    const columns = getCommonColumns(tableName);
    return { columns, hasData: false };

  } catch (error) {
    console.error(`Error analyzing ${tableName}:`, error.message);
    return { columns: getCommonColumns(tableName), hasData: false };
  }
}

function inferType(value) {
  if (value === null) return 'text';
  if (typeof value === 'string') {
    if (value.match(/^\d{4}-\d{2}-\d{2}/)) return 'date';
    if (value.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) return 'uuid';
    return 'text';
  }
  if (typeof value === 'number') return Number.isInteger(value) ? 'integer' : 'numeric';
  if (typeof value === 'boolean') return 'boolean';
  return 'text';
}

function parseErrorForColumns(errorMessage, tableName) {
  const columns = [];
  
  // Common error patterns that reveal column names
  if (errorMessage.includes('null value in column')) {
    const match = errorMessage.match(/null value in column "([^"]+)"/);
    if (match) {
      columns.push({ name: match[1], type: 'text', nullable: false, default: null });
    }
  }
  
  return columns;
}

function getCommonColumns(tableName) {
  const baseColumns = [
    { name: 'id', type: 'uuid', nullable: false, default: 'gen_random_uuid()' },
    { name: 'created_at', type: 'timestamp', nullable: false, default: 'now()' },
    { name: 'updated_at', type: 'timestamp', nullable: false, default: 'now()' }
  ];

  const specificColumns = {
    profiles: [
      { name: 'email', type: 'text', nullable: false, default: null },
      { name: 'full_name', type: 'text', nullable: true, default: null },
      { name: 'role', type: 'text', nullable: false, default: 'student' }
    ],
    students: [
      { name: 'first_name', type: 'text', nullable: false, default: null },
      { name: 'last_name', type: 'text', nullable: false, default: null },
      { name: 'date_of_birth', type: 'date', nullable: true, default: null },
      { name: 'gender', type: 'text', nullable: true, default: null },
      { name: 'email', type: 'text', nullable: true, default: null },
      { name: 'phone_number', type: 'text', nullable: true, default: null },
      { name: 'address', type: 'text', nullable: true, default: null },
      { name: 'class_id', type: 'uuid', nullable: true, default: null },
      { name: 'section_id', type: 'uuid', nullable: true, default: null },
      { name: 'academic_year_id', type: 'uuid', nullable: true, default: null },
      { name: 'is_active', type: 'boolean', nullable: false, default: true }
    ],
    classes: [
      { name: 'name', type: 'text', nullable: false, default: null },
      { name: 'description', type: 'text', nullable: true, default: null },
      { name: 'grade_level', type: 'integer', nullable: true, default: null },
      { name: 'is_active', type: 'boolean', nullable: false, default: true }
    ],
    sections: [
      { name: 'name', type: 'text', nullable: false, default: null },
      { name: 'class_id', type: 'uuid', nullable: false, default: null },
      { name: 'max_capacity', type: 'integer', nullable: true, default: null },
      { name: 'is_active', type: 'boolean', nullable: false, default: true }
    ],
    academic_years: [
      { name: 'tenant_id', type: 'uuid', nullable: true, default: null },
      { name: 'name', type: 'text', nullable: false, default: null },
      { name: 'start_date', type: 'date', nullable: false, default: null },
      { name: 'end_date', type: 'date', nullable: false, default: null },
      { name: 'is_current', type: 'boolean', nullable: false, default: false },
      { name: 'is_active', type: 'boolean', nullable: false, default: true }
    ],
    guardian_relations: [
      { name: 'name', type: 'text', nullable: false, default: null },
      { name: 'is_active', type: 'boolean', nullable: false, default: true }
    ],
    guardians: [
      { name: 'student_id', type: 'uuid', nullable: false, default: null },
      { name: 'name', type: 'text', nullable: false, default: null },
      { name: 'relation_id', type: 'uuid', nullable: false, default: null },
      { name: 'phone', type: 'text', nullable: true, default: null },
      { name: 'email', type: 'text', nullable: true, default: null },
      { name: 'address', type: 'text', nullable: true, default: null },
      { name: 'is_primary', type: 'boolean', nullable: false, default: false },
      { name: 'is_active', type: 'boolean', nullable: false, default: true }
    ],
    documents: [
      { name: 'student_id', type: 'uuid', nullable: false, default: null },
      { name: 'type', type: 'text', nullable: false, default: null },
      { name: 'file_name', type: 'text', nullable: false, default: null },
      { name: 'file_path', type: 'text', nullable: false, default: null },
      { name: 'file_url', type: 'text', nullable: true, default: null },
      { name: 'file_size', type: 'integer', nullable: true, default: null },
      { name: 'mime_type', type: 'text', nullable: true, default: null },
      { name: 'is_required', type: 'boolean', nullable: false, default: false },
      { name: 'status', type: 'text', nullable: false, default: 'pending' },
      { name: 'is_active', type: 'boolean', nullable: false, default: true }
    ],
    academic_records: [
      { name: 'student_id', type: 'uuid', nullable: false, default: null },
      { name: 'class_id', type: 'uuid', nullable: false, default: null },
      { name: 'section_id', type: 'uuid', nullable: true, default: null },
      { name: 'academic_year_id', type: 'uuid', nullable: false, default: null },
      { name: 'roll_number', type: 'text', nullable: true, default: null },
      { name: 'previous_school', type: 'text', nullable: true, default: null },
      { name: 'previous_percentage', type: 'numeric', nullable: true, default: null },
      { name: 'is_active', type: 'boolean', nullable: false, default: true }
    ]
  };

  return [...baseColumns, ...(specificColumns[tableName] || [])];
}

async function generateFullSchema() {
  console.log('🚀 Generating comprehensive schema...\n');
  
  const schemaInfo = {
    tables: {},
    tableGroups: {
      core: ['students', 'guardians', 'documents', 'academic_records'],
      master_data: ['classes', 'sections', 'academic_years', 'guardian_relations'],
      auth: ['profiles'],
      academic: ['enrollments', 'teachers', 'subjects'],
      tracking: ['attendance', 'grades']
    }
  };

  for (const tableName of knownTables) {
    const tableInfo = await getTableStructure(tableName);
    schemaInfo.tables[tableName] = tableInfo;
    
    console.log(`✅ ${tableName}: ${tableInfo.columns.length} columns${tableInfo.hasData ? ' (with data)' : ' (empty)'}`);
  }

  // Save comprehensive schema
  const outputPath = path.join(__dirname, '..', 'full-schema.json');
  fs.writeFileSync(outputPath, JSON.stringify(schemaInfo, null, 2));
  
  console.log('\n✅ Full schema saved to full-schema.json');
  console.log(`📊 Total tables: ${Object.keys(schemaInfo.tables).length}`);
  
  return schemaInfo;
}

generateFullSchema().then(() => {
  console.log('🎉 Schema generation completed!');
}).catch(console.error);
