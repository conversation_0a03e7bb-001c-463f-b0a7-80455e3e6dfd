#!/usr/bin/env node

/**
 * Alternative script to fetch Supabase schema using Management API
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAccessToken = process.env.SUPABASE_ACCESS_TOKEN;
const projectId = process.env.SUPABASE_PROJECT_ID;

if (!supabaseUrl || !supabaseAccessToken || !projectId) {
  console.error('Missing Supabase credentials in .env file');
  console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_ACCESS_TOKEN, SUPABASE_PROJECT_ID');
  process.exit(1);
}

function makeRequest(url, headers) {
  return new Promise((resolve, reject) => {
    const req = https.get(url, { headers }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          resolve(data);
        }
      });
    });
    req.on('error', reject);
  });
}

async function fetchSchemaViaAPI() {
  try {
    console.log('🔍 Fetching schema via Supabase Management API...');
    
    const headers = {
      'Authorization': `Bearer ${supabaseAccessToken}`,
      'Content-Type': 'application/json'
    };

    // Try to get project info first
    const projectUrl = `https://api.supabase.com/v1/projects/${projectId}`;
    console.log('📡 Getting project info...');
    
    const projectInfo = await makeRequest(projectUrl, headers);
    console.log('Project info:', projectInfo);

    // Try to get database schema
    const schemaUrl = `https://api.supabase.com/v1/projects/${projectId}/database/tables`;
    console.log('📊 Getting database tables...');
    
    const tablesInfo = await makeRequest(schemaUrl, headers);
    console.log('Tables info:', tablesInfo);

    return tablesInfo;

  } catch (error) {
    console.error('❌ Error fetching schema via API:', error);
  }
}

// Alternative: Try to connect directly to the database
async function fetchSchemaDirectly() {
  try {
    console.log('🔍 Trying direct database connection...');
    
    // Use the anon key to try basic table access
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(supabaseUrl, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);

    // List of common table names to check
    const commonTables = [
      'profiles', 'users', 'students', 'classes', 'sections', 
      'academic_years', 'guardian_relations', 'guardians',
      'documents', 'academic_records', 'enrollments',
      'teachers', 'subjects', 'attendance', 'grades'
    ];

    const existingTables = [];
    const schemaInfo = {
      tables: {},
      tableGroups: {
        core: [],
        master_data: [],
        system: [],
        auth: [],
        future: []
      }
    };

    console.log('🔍 Checking for existing tables...');
    
    for (const tableName of commonTables) {
      try {
        // Try to access the table
        const { data, error, count } = await supabase
          .from(tableName)
          .select('*', { count: 'exact', head: true });

        if (!error) {
          console.log(`✅ Found table: ${tableName} (${count || 0} rows)`);
          existingTables.push(tableName);
          
          // Try to get a sample row to understand structure
          const { data: sample } = await supabase
            .from(tableName)
            .select('*')
            .limit(1);

          const columns = [];
          if (sample && sample.length > 0) {
            Object.keys(sample[0]).forEach(key => {
              columns.push({
                name: key,
                type: typeof sample[0][key],
                nullable: true,
                default: null
              });
            });
          }

          schemaInfo.tables[tableName] = { columns };

          // Categorize table
          if (['students', 'guardians', 'academic_records', 'documents'].includes(tableName)) {
            schemaInfo.tableGroups.core.push(tableName);
          } else if (['classes', 'sections', 'academic_years', 'guardian_relations'].includes(tableName)) {
            schemaInfo.tableGroups.master_data.push(tableName);
          } else if (['profiles', 'users'].includes(tableName)) {
            schemaInfo.tableGroups.auth.push(tableName);
          } else if (['settings', 'logs', 'sessions'].includes(tableName)) {
            schemaInfo.tableGroups.system.push(tableName);
          } else {
            schemaInfo.tableGroups.future.push(tableName);
          }
        }
      } catch (err) {
        // Table doesn't exist or no access
        console.log(`❌ Table not accessible: ${tableName}`);
      }
    }

    if (existingTables.length > 0) {
      // Save schema information
      const outputPath = path.join(__dirname, '..', 'schema-output.json');
      fs.writeFileSync(outputPath, JSON.stringify(schemaInfo, null, 2));
      
      console.log('\n✅ Schema information saved to schema-output.json');
      console.log(`📊 Found ${existingTables.length} accessible tables:`);
      existingTables.forEach(table => console.log(`  - ${table}`));
      
      return schemaInfo;
    } else {
      console.log('❌ No accessible tables found');
      return null;
    }

  } catch (error) {
    console.error('❌ Error with direct connection:', error);
    return null;
  }
}

// Run both approaches
async function main() {
  console.log('🚀 Starting Supabase schema fetch...\n');
  
  // Try API approach first
  await fetchSchemaViaAPI();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Try direct approach
  const result = await fetchSchemaDirectly();
  
  if (result) {
    console.log('\n🎉 Schema fetch completed successfully!');
  } else {
    console.log('\n❌ Schema fetch failed. Please check your Supabase configuration.');
  }
}

main().catch(console.error);
