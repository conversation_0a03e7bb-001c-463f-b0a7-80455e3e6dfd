// src/services/core/errorHandler.ts

/**
 * Standardized error codes for the application
 */
export enum ErrorCode {
  // Validation Errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  REQUIRED_FIELD_MISSING = 'REQUIRED_FIELD_MISSING',
  INVALID_FORMAT = 'INVALID_FORMAT',
  DUPLICATE_VALUE = 'DUPLICATE_VALUE',
  
  // Database Errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  TRANSACTION_ERROR = 'TRANSACTION_ERROR',
  CONSTRAINT_VIOLATION = 'CONSTRAINT_VIOLATION',
  DATA_INTEGRITY_ERROR = 'DATA_INTEGRITY_ERROR',
  
  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  SESSION_NOT_FOUND = 'SESSION_NOT_FOUND',
  
  // Resource Errors
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',
  RESOURCE_LOCKED = 'RESOURCE_LOCKED',
  
  // File & Storage Errors
  FILE_UPLOAD_ERROR = 'FILE_UPLOAD_ERROR',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',
  
  // Network & External Service Errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  EXTERNAL_SERVICE_ERROR = 'EXTERNAL_SERVICE_ERROR',
  
  // Business Logic Errors
  BUSINESS_RULE_VIOLATION = 'BUSINESS_RULE_VIOLATION',
  ENROLLMENT_LIMIT_EXCEEDED = 'ENROLLMENT_LIMIT_EXCEEDED',
  ROLL_NUMBER_TAKEN = 'ROLL_NUMBER_TAKEN',
  INVALID_ACADEMIC_YEAR = 'INVALID_ACADEMIC_YEAR',
  
  // System Errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}

/**
 * Custom service error class with structured error information
 */
export class ServiceError extends Error {
  public readonly code: ErrorCode;
  public readonly context?: string;
  public readonly originalError?: any;
  public readonly timestamp: string;
  public readonly userMessage?: string;

  constructor(
    code: ErrorCode,
    message: string,
    originalError?: any,
    context?: string,
    userMessage?: string
  ) {
    super(message);
    this.name = 'ServiceError';
    this.code = code;
    this.context = context;
    this.originalError = originalError;
    this.timestamp = new Date().toISOString();
    this.userMessage = userMessage || this.getDefaultUserMessage(code);

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ServiceError);
    }
  }

  /**
   * Get user-friendly error message based on error code
   */
  private getDefaultUserMessage(code: ErrorCode): string {
    const messages: Record<ErrorCode, string> = {
      [ErrorCode.VALIDATION_ERROR]: 'Please check your input and try again.',
      [ErrorCode.REQUIRED_FIELD_MISSING]: 'Please fill in all required fields.',
      [ErrorCode.INVALID_FORMAT]: 'Please check the format of your input.',
      [ErrorCode.DUPLICATE_VALUE]: 'This value already exists. Please choose a different one.',
      
      [ErrorCode.DATABASE_ERROR]: 'A database error occurred. Please try again.',
      [ErrorCode.CONNECTION_ERROR]: 'Connection failed. Please check your internet connection.',
      [ErrorCode.TRANSACTION_ERROR]: 'Operation failed. Please try again.',
      [ErrorCode.CONSTRAINT_VIOLATION]: 'This operation violates data constraints.',
      [ErrorCode.DATA_INTEGRITY_ERROR]: 'Data integrity error. Please check your input.',
      
      [ErrorCode.UNAUTHORIZED]: 'You are not authorized to perform this action.',
      [ErrorCode.FORBIDDEN]: 'Access denied.',
      [ErrorCode.SESSION_EXPIRED]: 'Your session has expired. Please log in again.',
      [ErrorCode.SESSION_NOT_FOUND]: 'Session not found. Please start a new session.',
      
      [ErrorCode.NOT_FOUND]: 'The requested resource was not found.',
      [ErrorCode.ALREADY_EXISTS]: 'This resource already exists.',
      [ErrorCode.RESOURCE_LOCKED]: 'This resource is currently locked.',
      
      [ErrorCode.FILE_UPLOAD_ERROR]: 'File upload failed. Please try again.',
      [ErrorCode.FILE_TOO_LARGE]: 'File is too large. Please choose a smaller file.',
      [ErrorCode.INVALID_FILE_TYPE]: 'Invalid file type. Please choose a supported file.',
      [ErrorCode.STORAGE_QUOTA_EXCEEDED]: 'Storage quota exceeded. Please contact administrator.',
      
      [ErrorCode.NETWORK_ERROR]: 'Network error. Please check your connection.',
      [ErrorCode.TIMEOUT_ERROR]: 'Operation timed out. Please try again.',
      [ErrorCode.EXTERNAL_SERVICE_ERROR]: 'External service error. Please try again later.',
      
      [ErrorCode.BUSINESS_RULE_VIOLATION]: 'This operation violates business rules.',
      [ErrorCode.ENROLLMENT_LIMIT_EXCEEDED]: 'Enrollment limit has been exceeded.',
      [ErrorCode.ROLL_NUMBER_TAKEN]: 'This roll number is already taken.',
      [ErrorCode.INVALID_ACADEMIC_YEAR]: 'Invalid academic year selected.',
      
      [ErrorCode.UNKNOWN_ERROR]: 'An unexpected error occurred. Please try again.',
      [ErrorCode.CONFIGURATION_ERROR]: 'System configuration error. Please contact administrator.',
      [ErrorCode.SERVICE_UNAVAILABLE]: 'Service is temporarily unavailable. Please try again later.'
    };

    return messages[code] || 'An error occurred. Please try again.';
  }

  /**
   * Convert error to JSON for logging or API responses
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      userMessage: this.userMessage,
      context: this.context,
      timestamp: this.timestamp,
      stack: this.stack
    };
  }

  /**
   * Check if error is retryable
   */
  isRetryable(): boolean {
    const retryableCodes = [
      ErrorCode.NETWORK_ERROR,
      ErrorCode.TIMEOUT_ERROR,
      ErrorCode.CONNECTION_ERROR,
      ErrorCode.SERVICE_UNAVAILABLE
    ];

    return retryableCodes.includes(this.code);
  }

  /**
   * Check if error should be reported to monitoring
   */
  shouldReport(): boolean {
    const nonReportableCodes = [
      ErrorCode.VALIDATION_ERROR,
      ErrorCode.REQUIRED_FIELD_MISSING,
      ErrorCode.INVALID_FORMAT,
      ErrorCode.UNAUTHORIZED,
      ErrorCode.FORBIDDEN,
      ErrorCode.NOT_FOUND
    ];

    return !nonReportableCodes.includes(this.code);
  }
}

/**
 * Error handler utility class
 */
export class ErrorHandler {
  /**
   * Handle and transform various error types into ServiceError
   */
  static handle(error: any, context?: string): ServiceError {
    if (error instanceof ServiceError) {
      return error;
    }

    // Handle Supabase errors
    if (error?.code) {
      return this.handleSupabaseError(error, context);
    }

    // Handle network errors
    if (error?.name === 'NetworkError' || error?.code === 'NETWORK_ERROR') {
      return new ServiceError(
        ErrorCode.NETWORK_ERROR,
        'Network connection failed',
        error,
        context
      );
    }

    // Handle timeout errors
    if (error?.name === 'TimeoutError' || error?.code === 'TIMEOUT') {
      return new ServiceError(
        ErrorCode.TIMEOUT_ERROR,
        'Operation timed out',
        error,
        context
      );
    }

    // Handle validation errors
    if (error?.name === 'ValidationError') {
      return new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        error.message || 'Validation failed',
        error,
        context
      );
    }

    // Default to unknown error
    return new ServiceError(
      ErrorCode.UNKNOWN_ERROR,
      error?.message || 'An unknown error occurred',
      error,
      context
    );
  }

  /**
   * Handle Supabase-specific errors
   */
  private static handleSupabaseError(error: any, context?: string): ServiceError {
    const { code, message } = error;

    switch (code) {
      case 'PGRST116':
        return new ServiceError(ErrorCode.NOT_FOUND, message, error, context);
      
      case '23505': // Unique constraint violation
        return new ServiceError(ErrorCode.DUPLICATE_VALUE, message, error, context);
      
      case '23503': // Foreign key constraint violation
        return new ServiceError(ErrorCode.CONSTRAINT_VIOLATION, message, error, context);
      
      case '23514': // Check constraint violation
        return new ServiceError(ErrorCode.VALIDATION_ERROR, message, error, context);
      
      case 'PGRST301': // Permission denied
        return new ServiceError(ErrorCode.FORBIDDEN, message, error, context);
      
      case 'PGRST302': // Unauthorized
        return new ServiceError(ErrorCode.UNAUTHORIZED, message, error, context);
      
      default:
        return new ServiceError(ErrorCode.DATABASE_ERROR, message, error, context);
    }
  }

  /**
   * Log error with appropriate level
   */
  static log(error: ServiceError): void {
    const logData = {
      ...error.toJSON(),
      originalError: error.originalError
    };

    if (error.shouldReport()) {
      console.error('Service Error:', logData);
      // TODO: Send to monitoring service (e.g., Sentry, LogRocket)
    } else {
      console.warn('Service Warning:', logData);
    }
  }

  /**
   * Create user-safe error response
   */
  static createUserResponse(error: ServiceError): {
    success: false;
    error: {
      code: string;
      message: string;
      retryable: boolean;
    };
  } {
    return {
      success: false,
      error: {
        code: error.code,
        message: error.userMessage || error.message,
        retryable: error.isRetryable()
      }
    };
  }
}

/**
 * Retry utility for handling retryable errors
 */
export class RetryHandler {
  static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delayMs: number = 1000
  ): Promise<T> {
    let lastError: ServiceError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = ErrorHandler.handle(error, `Retry attempt ${attempt}/${maxRetries}`);
        
        if (!lastError.isRetryable() || attempt === maxRetries) {
          throw lastError;
        }

        // Exponential backoff
        const delay = delayMs * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }
}
