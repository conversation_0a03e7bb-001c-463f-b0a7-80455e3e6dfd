// src/services/serviceFactory.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';
import { Database } from '../types/database';

// Core services
import { DatabaseService } from './core/databaseService';

// New consolidated services
import { DocumentManagementService } from './documentManagementService';
import { EnrollmentManagementService } from './enrollmentManagementService';
import { StudentManagementService } from './studentManagementService';

// Legacy services (for backward compatibility)
import { ValidationService } from './enrollment/validationService';

/**
 * Service container interface - Updated for new architecture
 */
export interface ServiceContainer {
  // Core services
  databaseService: DatabaseService;

  // New consolidated services
  studentManagementService: StudentManagementService;
  enrollmentManagementService: EnrollmentManagementService;
  documentManagementService: DocumentManagementService;

  // Legacy services (for backward compatibility)
  validationService: ValidationService;
}

/**
 * Service factory for creating and managing service instances
 * Implements dependency injection pattern for clean architecture
 */
export class ServiceFactory {
  private static instance: ServiceFactory;
  private container: ServiceContainer | null = null;
  private client: SupabaseClient<Database>;

  private constructor(client: SupabaseClient<Database>) {
    this.client = client;
  }

  /**
   * Get singleton instance of ServiceFactory
   */
  static getInstance(client?: SupabaseClient<Database>): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory(client || supabase);
    }
    return ServiceFactory.instance;
  }

  /**
   * Initialize all services and create container
   */
  async initialize(): Promise<ServiceContainer> {
    if (this.container) {
      return this.container;
    }

    try {
      console.log('Initializing service container with new architecture...');

      // Initialize core services
      const databaseService = DatabaseService.getInstance();

      // Test database connection
      const healthStatus = await databaseService.getHealthStatus();
      if (healthStatus.status !== 'healthy') {
        throw new Error('Database connection is not healthy');
      }

      // Initialize new consolidated services
      const studentManagementService = new StudentManagementService(this.client);
      const enrollmentManagementService = new EnrollmentManagementService(this.client);
      const documentManagementService = new DocumentManagementService(this.client);

      // Initialize legacy services (for backward compatibility)
      const validationService = new ValidationService(this.client);

      // Create service container
      this.container = {
        databaseService,
        studentManagementService,
        enrollmentManagementService,
        documentManagementService,
        validationService
      };

      console.log('✓ Service container initialized successfully with new architecture');
      return this.container;

    } catch (error) {
      console.error('Failed to initialize service container:', error);
      throw error;
    }
  }

  /**
   * Get service container (initialize if not already done)
   */
  async getContainer(): Promise<ServiceContainer> {
    if (!this.container) {
      return await this.initialize();
    }
    return this.container;
  }

  /**
   * Get specific service by name
   */
  async getService<K extends keyof ServiceContainer>(
    serviceName: K
  ): Promise<ServiceContainer[K]> {
    const container = await this.getContainer();
    return container[serviceName];
  }

  /**
   * Reset service container (useful for testing)
   */
  reset(): void {
    this.container = null;
  }

  /**
   * Health check for all services
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    services: Record<string, { status: string; latency?: number; error?: string }>;
  }> {
    const serviceStatus: Record<string, { status: string; latency?: number; error?: string }> = {};
    let overallStatus: 'healthy' | 'unhealthy' = 'healthy';

    try {
      const container = await this.getContainer();

      // Check database service
      try {
        const startTime = Date.now();
        const dbHealth = await container.databaseService.getHealthStatus();
        serviceStatus.database = {
          status: dbHealth.status,
          latency: Date.now() - startTime
        };
        if (dbHealth.status !== 'healthy') {
          overallStatus = 'unhealthy';
        }
      } catch (error) {
        serviceStatus.database = {
          status: 'unhealthy',
          error: error instanceof Error ? error.message : 'Unknown error'
        };
        overallStatus = 'unhealthy';
      }

      // Check new consolidated services
      const consolidatedServices = [
        'studentManagementService',
        'enrollmentManagementService',
        'documentManagementService',
        'validationService'
      ];

      for (const serviceName of consolidatedServices) {
        serviceStatus[serviceName] = {
          status: serviceStatus.database.status === 'healthy' ? 'healthy' : 'unhealthy'
        };
      }

    } catch (error) {
      overallStatus = 'unhealthy';
      serviceStatus.container = {
        status: 'unhealthy',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }

    return {
      status: overallStatus,
      services: serviceStatus
    };
  }
}

/**
 * Convenience functions for getting services
 */

// Global service factory instance
let globalServiceFactory: ServiceFactory | null = null;

/**
 * Get global service factory instance
 */
export function getServiceFactory(): ServiceFactory {
  if (!globalServiceFactory) {
    globalServiceFactory = ServiceFactory.getInstance();
  }
  return globalServiceFactory;
}

/**
 * Get service container
 */
export async function getServices(): Promise<ServiceContainer> {
  const factory = getServiceFactory();
  return await factory.getContainer();
}

/**
 * Get specific service
 */
export async function getService<K extends keyof ServiceContainer>(
  serviceName: K
): Promise<ServiceContainer[K]> {
  const factory = getServiceFactory();
  return await factory.getService(serviceName);
}

/**
 * Initialize services (call this in your app initialization)
 */
export async function initializeServices(): Promise<ServiceContainer> {
  try {
    const factory = getServiceFactory();
    const container = await factory.initialize();

    // Perform health check
    const health = await factory.healthCheck();
    if (health.status !== 'healthy') {
      // Log warning for unhealthy services in development
      if (process.env.NODE_ENV === 'development') {
        console.warn('Some services are not healthy:', health.services);
      }
    }

    return container;
  } catch (error) {
    // Only log in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Failed to initialize services:', error);
    }
    throw error;
  }
}

/**
 * Service hooks for React components
 */

/**
 * Hook to get service container
 */
export function useServices(): Promise<ServiceContainer> {
  return getServices();
}

/**
 * Hook to get specific service
 */
export function useService<K extends keyof ServiceContainer>(
  serviceName: K
): Promise<ServiceContainer[K]> {
  return getService(serviceName);
}

/**
 * Service health monitoring
 */
export async function monitorServiceHealth(): Promise<void> {
  const factory = getServiceFactory();

  setInterval(async () => {
    try {
      const health = await factory.healthCheck();
      if (health.status !== 'healthy') {
        // Only log in development, send to monitoring service in production
        if (process.env.NODE_ENV === 'development') {
          console.warn('Service health check failed:', health);
        }
        // TODO: Send to monitoring service (e.g., Sentry, DataDog)
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Health check error:', error);
      }
      // TODO: Send to monitoring service
    }
  }, 60000); // Check every minute
}

/**
 * Graceful shutdown
 */
export async function shutdownServices(): Promise<void> {
  try {
    console.log('Shutting down services...');

    // Cleanup any resources
    const factory = getServiceFactory();
    factory.reset();

    console.log('✓ Services shut down successfully');
  } catch (error) {
    console.error('Error during service shutdown:', error);
  }
}

// Export default factory instance
export default getServiceFactory();
