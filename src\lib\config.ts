// src/lib/config.ts

/**
 * Application configuration
 * Centralized configuration for the entire application
 */

// Environment variables with defaults
export const env = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
} as const;

// Application metadata
export const appConfig = {
  name: 'EduPro',
  description: 'School Management System',
  version: '1.0.0',
  author: 'EduPro Team',
  url: env.NEXT_PUBLIC_APP_URL,
  supportEmail: '<EMAIL>',
} as const;

// Feature flags
export const features = {
  enableEnhancedEnrollment: true,
  enableDocumentManagement: true,
  enableBulkOperations: true,
  enableAdvancedSearch: true,
  enableNotifications: false, // Coming soon
  enableReporting: false, // Coming soon
} as const;

// UI Configuration
export const uiConfig = {
  // Theme
  theme: {
    defaultMode: 'light' as const,
    enableDarkMode: true,
  },
  
  // Layout
  layout: {
    sidebarWidth: 280,
    headerHeight: 64,
    maxContentWidth: 1200,
  },
  
  // Pagination
  pagination: {
    defaultPageSize: 20,
    pageSizeOptions: [10, 20, 50, 100],
    maxPageSize: 100,
  },
  
  // Tables
  table: {
    defaultSortOrder: 'desc' as const,
    enableColumnResizing: true,
    enableColumnReordering: true,
  },
  
  // Forms
  forms: {
    autoSave: true,
    autoSaveDelay: 2000, // 2 seconds
    showRequiredIndicator: true,
  },
  
  // Notifications
  notifications: {
    position: 'top-right' as const,
    duration: 5000, // 5 seconds
    maxVisible: 3,
  },
} as const;

// API Configuration
export const apiConfig = {
  timeout: 30000, // 30 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
  
  // Rate limiting
  rateLimit: {
    requests: 100,
    window: 60000, // 1 minute
  },
  
  // Cache
  cache: {
    defaultTTL: 300000, // 5 minutes
    maxSize: 100, // 100 entries
  },
} as const;

// File upload configuration
export const uploadConfig = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedFileTypes: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
  ],
  
  // Image specific
  images: {
    maxWidth: 2048,
    maxHeight: 2048,
    quality: 0.8,
    formats: ['jpeg', 'png', 'webp'],
  },
  
  // Document specific
  documents: {
    maxPages: 50,
    allowedExtensions: ['.pdf', '.doc', '.docx', '.txt'],
  },
} as const;

// Validation configuration
export const validationConfig = {
  // Student validation
  student: {
    minAge: 3,
    maxAge: 25,
    nameMinLength: 2,
    nameMaxLength: 50,
    rollNumberMaxLength: 20,
  },
  
  // Guardian validation
  guardian: {
    nameMinLength: 2,
    nameMaxLength: 100,
    phoneMinLength: 10,
    phoneMaxLength: 15,
  },
  
  // Password validation
  password: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
  },
} as const;

// Database configuration
export const dbConfig = {
  // Connection
  connectionTimeout: 30000, // 30 seconds
  queryTimeout: 60000, // 60 seconds
  maxRetries: 3,
  retryDelay: 1000, // 1 second
  
  // Pagination
  defaultPageSize: 20,
  maxPageSize: 100,
  
  // Session
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  
  // Storage
  storageBucket: 'student-documents',
} as const;

// Security configuration
export const securityConfig = {
  // Session
  sessionCookieName: 'edupro-session',
  sessionMaxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  
  // CSRF
  csrfTokenName: 'csrf-token',
  
  // Rate limiting
  rateLimitWindowMs: 15 * 60 * 1000, // 15 minutes
  rateLimitMaxRequests: 100,
  
  // Password
  passwordSaltRounds: 12,
  passwordResetTokenExpiry: 60 * 60 * 1000, // 1 hour
  
  // JWT
  jwtExpiresIn: '7d',
  jwtRefreshExpiresIn: '30d',
} as const;

// Email configuration
export const emailConfig = {
  from: '<EMAIL>',
  replyTo: '<EMAIL>',
  
  // Templates
  templates: {
    welcome: 'welcome',
    passwordReset: 'password-reset',
    enrollmentConfirmation: 'enrollment-confirmation',
    documentApproval: 'document-approval',
  },
  
  // Settings
  enableEmailNotifications: true,
  batchSize: 50,
  retryAttempts: 3,
} as const;

// Monitoring and logging
export const monitoringConfig = {
  // Logging
  logLevel: env.NODE_ENV === 'production' ? 'warn' : 'debug',
  enableConsoleLogging: env.NODE_ENV !== 'production',
  enableFileLogging: env.NODE_ENV === 'production',
  
  // Performance monitoring
  enablePerformanceMonitoring: true,
  performanceThreshold: 1000, // 1 second
  
  // Error tracking
  enableErrorTracking: true,
  errorSampleRate: env.NODE_ENV === 'production' ? 0.1 : 1.0,
  
  // Analytics
  enableAnalytics: env.NODE_ENV === 'production',
  analyticsProvider: 'google-analytics',
} as const;

// Development configuration
export const devConfig = {
  enableDebugMode: env.NODE_ENV === 'development',
  enableHotReload: env.NODE_ENV === 'development',
  enableSourceMaps: env.NODE_ENV !== 'production',
  enableProfiling: false,
  
  // Mock data
  enableMockData: env.NODE_ENV === 'development',
  mockDataSeed: 12345,
  
  // Testing
  enableTestMode: process.env.NODE_ENV === 'test',
  testTimeout: 30000, // 30 seconds
} as const;

// Export all configurations
export const config = {
  env,
  app: appConfig,
  features,
  ui: uiConfig,
  api: apiConfig,
  upload: uploadConfig,
  validation: validationConfig,
  db: dbConfig,
  security: securityConfig,
  email: emailConfig,
  monitoring: monitoringConfig,
  dev: devConfig,
} as const;

// Type exports
export type Config = typeof config;
export type AppConfig = typeof appConfig;
export type UIConfig = typeof uiConfig;
export type APIConfig = typeof apiConfig;

// Validation functions
export function validateConfig(): void {
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  ];
  
  const missingVars = requiredEnvVars.filter(
    varName => !env[varName as keyof typeof env]
  );
  
  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}`
    );
  }
}

// Initialize configuration
if (typeof window === 'undefined') {
  // Server-side only
  try {
    validateConfig();
  } catch (error) {
    console.error('Configuration validation failed:', error);
    if (env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }
}

export default config;
