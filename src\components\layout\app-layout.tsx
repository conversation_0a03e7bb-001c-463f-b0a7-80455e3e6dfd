// src/components/layout/app-layout.tsx
'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useAuth } from '../auth/auth-provider';
import AppContent from './app-content';
import AppFooter from './app-footer';
import AppHeader from './app-header';
import AppSidebar from './app-sidebar';

interface AppLayoutProps {
  children: React.ReactNode;
  title?: string;
  showFooter?: boolean;
}

const AppLayout = ({ children, title, showFooter = false }: AppLayoutProps) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user, loading } = useAuth();

  // Get current route for breadcrumb
  const getCurrentRoute = () => {
    const route = pathname.split('/')[1];
    return route || 'dashboard';
  };

  // Get page title based on route
  const getPageTitle = () => {
    if (title) return title;
    
    const routeTitles: Record<string, string> = {
      'dashboard': 'Dashboard',
      'student-management': 'Student Management',
      'staff-management': 'Staff Management',
      'academic-management': 'Academic Management',
      'attendance-management': 'Attendance Management',
      'fee-management': 'Fee Management',
    };

    const route = getCurrentRoute();
    return routeTitles[route] || 'Dashboard';
  };

  useEffect(() => {
    // Only redirect if definitely not authenticated and not loading
    // Let middleware handle the redirects instead of aggressive client-side redirects
    if (!loading && !user) {
      // Give a reasonable delay before redirecting to allow for session restoration
      const timeoutId = setTimeout(() => {
        router.push('/auth');
      }, 2000);

      return () => clearTimeout(timeoutId);
    }
  }, [user, loading, router]);

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>

          {/* Provide escape route */}
          <div className="mt-4">
            <button
              onClick={() => router.push('/auth')}
              className="text-blue-600 hover:text-blue-700 underline text-sm"
            >
              Go to Login →
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // useEffect will handle the redirect
  }

  return (
    <div className="flex h-screen bg-gray-100" style={{ overflow: 'visible' }}>
      {/* Sidebar - Fixed position, full height */}
      <div className={`flex-shrink-0 transition-all duration-300 ${
        isSidebarCollapsed ? 'w-16' : 'w-72'
      }`} style={{ overflow: 'visible', position: 'relative', zIndex: 1000 }}>
        <AppSidebar
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={toggleSidebar}
        />
      </div>

      {/* Main Content Area - Full height, starts from top */}
      <div className="flex flex-col flex-1 min-h-0 relative" style={{ marginLeft: '0', zIndex: 1 }}>
        {/* Header - Matches sidebar styling */}
        <div className="flex-shrink-0">
          <AppHeader
            title={getPageTitle()}
            currentRoute={getCurrentRoute()}
          />
        </div>

        {/* Page Content - Scrollable */}
        <AppContent>
          {children}
        </AppContent>

        {/* Footer - Optional */}
        {showFooter && (
          <div className="flex-shrink-0">
            <AppFooter />
          </div>
        )}

        {/* Fixed Educational Quote Footer - Only for main content area */}
        <div className="absolute bottom-0 left-0 right-0 z-30 bg-gradient-to-r from-blue-900 via-indigo-900 to-slate-900 backdrop-blur-md border-t border-blue-700/50 shadow-lg shadow-blue-900/20">
          <div className="px-5 py-1">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-7 h-7 bg-blue-600/20 rounded-full flex items-center justify-center flex-shrink-0">
                <svg className="w-3.5 h-3.5 text-blue-300" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                </svg>
              </div>
              <div className="text-center">
                <p className="text-sm font-medium text-blue-100 leading-relaxed">
                  "Education is the most powerful weapon which you can use to change the world."
                </p>
                <p className="text-xs text-blue-300/80 mt-0.5">
                  — Nelson Mandela
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppLayout;
