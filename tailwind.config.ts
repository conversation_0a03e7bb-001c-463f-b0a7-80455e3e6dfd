import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        'sans': [
          'Inter',
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          '"Segoe UI"',
          'Roboto',
          '"Helvetica Neue"',
          'Arial',
          '"Noto Sans"',
          'sans-serif',
          '"Apple Color Emoji"',
          '"Segoe UI Emoji"',
          '"Segoe UI Symbol"',
          '"Noto Color Emoji"'
        ],
      },      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1.25rem' }],    // Professional baseline
        'sm': ['0.875rem', { lineHeight: '1.375rem' }], // 14px
        'base': ['1rem', { lineHeight: '1.5rem' }],      // 16px standard
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],   // 18px
        'xl': ['1.25rem', { lineHeight: '1.875rem' }],   // 20px
        '2xl': ['1.5rem', { lineHeight: '2rem' }],       // 24px
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],  // 30px
        '4xl': ['2.25rem', { lineHeight: '2.625rem' }],  // 36px
        '5xl': ['3rem', { lineHeight: '3.5rem' }],       // 48px
        '6xl': ['3.75rem', { lineHeight: '4.25rem' }],   // 60px
        '7xl': ['4.5rem', { lineHeight: '5rem' }],       // 72px
        '8xl': ['6rem', { lineHeight: '6.5rem' }],       // 96px
        '9xl': ['8rem', { lineHeight: '8.5rem' }],       // 128px
      },
    },
  },
  plugins: [],
};
export default config;
