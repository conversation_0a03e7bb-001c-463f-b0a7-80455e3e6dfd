// src/services/masterDataService.ts
import { DATABASE_TABLES } from '../constants/database';
import { supabase } from '../lib/supabase';
import type { AcademicYear, Class, GuardianRelation, Section } from '../types/database';

export class MasterDataService {
  /**
   * Get all active classes
   */
  static async getClasses(): Promise<Class[]> {
    try {
      console.log('🔍 Fetching classes from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.CLASSES)
        .select(`
          id,
          name,
          description,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        console.error('❌ Error fetching classes from Supabase:', error);
        return [];
      }

      if (!data) {
        console.warn('⚠️ No classes data returned from database');
        return [];
      }

      console.log(`✅ Successfully fetched ${data.length} classes from Supabase`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch classes from Supabase:', error);
      return [];
    }
  }
  /**
   * Get all active sections
   */
  static async getSections(): Promise<Section[]> {
    try {
      console.log('🔍 Fetching sections from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.SECTIONS)
        .select(`
          id,
          name,
          description,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) {
        console.error('❌ Error fetching sections from Supabase:', error);
        return [];
      }

      if (!data) {
        console.warn('⚠️ No sections data returned from database');
        return [];
      }

      console.log(`✅ Successfully fetched ${data.length} sections from Supabase`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch sections from Supabase:', error);
      return [];
    }
  }
  /**
   * Get all active academic years
   */
  static async getAcademicYears(): Promise<AcademicYear[]> {
    try {
      console.log('🔍 Fetching academic years from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.ACADEMIC_YEARS)
        .select(`
          id,
          year,
          start_date,
          end_date,
          is_current,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_active', true)
        .order('start_date', { ascending: false });

      if (error) {
        console.error('❌ Error fetching academic years from Supabase:', error);
        return [];
      }

      if (!data) {
        console.warn('⚠️ No academic years data returned from database');
        return [];
      }

      console.log(`✅ Successfully fetched ${data.length} academic years from Supabase`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch academic years from Supabase:', error);
      return [];
    }
  }

  /**
   * Get current academic year
   */
  static async getCurrentAcademicYear(): Promise<AcademicYear | null> {
    try {
      console.log('🔍 Fetching current academic year from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.ACADEMIC_YEARS)
        .select(`
          id,
          year,
          start_date,
          end_date,
          is_current,
          is_active,
          created_at,
          updated_at
        `)
        .eq('is_current', true)
        .eq('is_active', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.warn('⚠️ No current academic year found in database');
          return null;
        }
        console.error('❌ Error fetching current academic year from Supabase:', error);
        return null;
      }

      console.log(`✅ Successfully fetched current academic year: ${data.year}`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch current academic year from Supabase:', error);
      return null;
    }
  }
  /**
   * Get all active guardian relations
   */
  static async getGuardianRelations(): Promise<GuardianRelation[]> {
    try {
      console.log('🔍 Fetching guardian relations from Supabase...');

      const { data, error } = await supabase
        .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
        .select(`
          ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.ID},
          ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.NAME},
          ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.IS_ACTIVE},
          ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.CREATED_AT},
          ${DATABASE_COLUMNS.GUARDIAN_RELATIONS.UPDATED_AT}
        `)
        .eq(DATABASE_COLUMNS.GUARDIAN_RELATIONS.IS_ACTIVE, true)
        .order(DATABASE_COLUMNS.GUARDIAN_RELATIONS.NAME, { ascending: true });

      if (error) {
        // If table doesn't exist, return empty array
        if (error.code === '42P01') {
          console.warn('⚠️ Guardian relations table does not exist');
          return [];
        }
        console.error('❌ Error fetching guardian relations from Supabase:', error);
        return [];
      }

      if (!data) {
        console.warn('⚠️ No guardian relations data returned from database');
        return [];
      }

      console.log(`✅ Successfully fetched ${data.length} guardian relations from Supabase`);
      return data;
    } catch (error) {
      console.error('❌ Failed to fetch guardian relations from Supabase:', error);
      return [];
    }
  }

  /**
   * Get all master data in one call for forms
   * Returns empty arrays for failed requests to ensure dropdowns show empty state
   */
  static async getAllMasterData() {
    try {
      console.log('🔄 Fetching all master data from Supabase...');
      console.log('📋 Using database tables:', {
        classes: DATABASE_TABLES.CLASSES,
        sections: DATABASE_TABLES.SECTIONS,
        academicYears: DATABASE_TABLES.ACADEMIC_YEARS,
        guardianRelations: DATABASE_TABLES.GUARDIAN_RELATIONS
      });

      // Use Promise.allSettled to handle individual failures gracefully
      const results = await Promise.allSettled([
        this.getClasses(),
        this.getSections(),
        this.getAcademicYears(),
        this.getGuardianRelations(),
        this.getCurrentAcademicYear()
      ]);

      // Extract results or use empty arrays/null for failures
      const classes = results[0].status === 'fulfilled' ? results[0].value : [];
      const sections = results[1].status === 'fulfilled' ? results[1].value : [];
      const academicYears = results[2].status === 'fulfilled' ? results[2].value : [];
      const guardianRelations = results[3].status === 'fulfilled' ? results[3].value : [];
      const currentAcademicYear = results[4].status === 'fulfilled' ? results[4].value : null;

      // Log any failures with detailed information
      results.forEach((result, index) => {
        const names = ['classes', 'sections', 'academicYears', 'guardianRelations', 'currentAcademicYear'];
        const tables = [DATABASE_TABLES.CLASSES, DATABASE_TABLES.SECTIONS, DATABASE_TABLES.ACADEMIC_YEARS, DATABASE_TABLES.GUARDIAN_RELATIONS, 'current_academic_year'];

        if (result.status === 'rejected') {
          console.warn(`⚠️  ${names[index]} (table: ${tables[index]}) failed:`, result.reason);
          console.warn(`💡 Check if table '${tables[index]}' exists and has proper permissions`);
        } else {
          const dataLength = Array.isArray(result.value) ? result.value.length : (result.value ? 1 : 0);
          console.log(`✅ ${names[index]} (table: ${tables[index]}): ${dataLength} records`);
        }
      });

      const masterData = {
        classes,
        sections,
        academicYears,
        guardianRelations,
        currentAcademicYear
      };

      console.log('✅ Master data loaded successfully:', {
        classes: masterData.classes.length,
        sections: masterData.sections.length,
        academicYears: masterData.academicYears.length,
        guardianRelations: masterData.guardianRelations.length,
        currentAcademicYear: masterData.currentAcademicYear?.year || 'None'
      });

      return masterData;
    } catch (error) {
      console.error('❌ Critical error fetching master data:', error);

      // Return empty data if everything fails
      const emptyData = {
        classes: [],
        sections: [],
        academicYears: [],
        guardianRelations: [],
        currentAcademicYear: null
      };

      console.log('🔄 Using empty master data due to critical error');
      return emptyData;
    }
  }

  /**
   * Check if roll number exists in a class and section for an academic year
   */
  static async isRollNumberExists(
    rollNumber: string, 
    classId: string, 
    sectionId: string, 
    academicYearId: string,
    excludeStudentId?: string
  ): Promise<boolean> {
    let query = supabase
      .from(DATABASE_TABLES.STUDENTS)
      .select('id')
      .eq('roll_number', rollNumber)
      .eq('class_id', classId)
      .eq('section_id', sectionId)
      .eq('academic_year_id', academicYearId)
      .eq('is_active', true);

    if (excludeStudentId) {
      query = query.neq('id', excludeStudentId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error checking roll number:', error);
      throw new Error(`Failed to check roll number: ${error.message}`);
    }

    return (data || []).length > 0;
  }
}
