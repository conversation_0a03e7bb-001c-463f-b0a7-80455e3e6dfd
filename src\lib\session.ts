// src/lib/session.ts
'use server';

import { cookies } from 'next/headers';
import { SignJWT, jwtVerify } from 'jose';
import { User } from './auth';

const secretKey = process.env.SESSION_SECRET || 'fallback-secret-key-for-development';
const encodedKey = new TextEncoder().encode(secretKey);

export interface SessionPayload {
  userId: string;
  email: string;
  name: string;
  role: string;
  expiresAt: Date;
}

/**
 * Encrypt session data into a JWT token
 */
export async function encrypt(payload: SessionPayload): Promise<string> {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('7d')
    .sign(encodedKey);
}

/**
 * Decrypt JWT token to get session data
 */
export async function decrypt(session: string | undefined = ''): Promise<SessionPayload | null> {
  try {
    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ['HS256'],
    });
    return payload as SessionPayload;
  } catch (error) {
    console.log('Failed to verify session:', error);
    return null;
  }
}

/**
 * Create a new session and store it in cookies
 */
export async function createSession(user: User): Promise<void> {
  const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 days
  const sessionPayload: SessionPayload = {
    userId: user.email, // Using email as userId for now
    email: user.email,
    name: user.name,
    role: user.role,
    expiresAt,
  };

  const session = await encrypt(sessionPayload);
  const cookieStore = await cookies();

  cookieStore.set('session', session, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    expires: expiresAt,
    sameSite: 'lax',
    path: '/',
  });
}

/**
 * Get session data from cookies
 */
export async function getSession(): Promise<SessionPayload | null> {
  const cookieStore = await cookies();
  const session = cookieStore.get('session')?.value;
  return await decrypt(session);
}

/**
 * Verify session and return user data
 */
export async function verifySession(): Promise<User | null> {
  const session = await getSession();
  
  if (!session) {
    return null;
  }

  // Check if session is expired
  if (new Date() > new Date(session.expiresAt)) {
    await deleteSession();
    return null;
  }

  return {
    name: session.name,
    email: session.email,
    role: session.role,
    isAuthenticated: true,
  };
}

/**
 * Update session with new data
 */
export async function updateSession(user: User): Promise<void> {
  const session = await getSession();
  
  if (!session) {
    return;
  }

  const updatedPayload: SessionPayload = {
    ...session,
    name: user.name,
    email: user.email,
    role: user.role,
  };

  const newSession = await encrypt(updatedPayload);
  const cookieStore = await cookies();

  cookieStore.set('session', newSession, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    expires: new Date(session.expiresAt),
    sameSite: 'lax',
    path: '/',
  });
}

/**
 * Delete session cookie
 */
export async function deleteSession(): Promise<void> {
  const cookieStore = await cookies();
  cookieStore.delete('session');
}
