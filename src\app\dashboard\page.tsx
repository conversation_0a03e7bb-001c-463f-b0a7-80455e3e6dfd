// src/app/dashboard/page.tsx
'use client';

import { Suspense } from 'react';
import { AppLayout } from '../../components/layout';
import DashboardContent from './_components/dashboard-content';

export default function DashboardPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-slate-600">Loading dashboard...</p>
        </div>
      </div>
    }>
      <AppLayout title="Dashboard">
        <DashboardContent />
      </AppLayout>
    </Suspense>
  );
}
