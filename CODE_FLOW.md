# EduPro Code Flow & Development Guide

This document provides a comprehensive guide for developers to understand the project structure, architecture patterns, and how to implement new features or modify existing ones.

## 📋 Table of Contents

1. [Project Architecture Overview](#project-architecture-overview)
2. [Directory Structure Deep Dive](#directory-structure-deep-dive)
3. [Code Flow Patterns](#code-flow-patterns)
4. [Creating New Features](#creating-new-features)
5. [Modifying Existing Features](#modifying-existing-features)
6. [Database Operations Flow](#database-operations-flow)
7. [UI Component Flow](#ui-component-flow)
8. [State Management Flow](#state-management-flow)
9. [File Organization Guidelines](#file-organization-guidelines)
10. [Common Development Patterns](#common-development-patterns)
11. [Testing Approach](#testing-approach)
12. [Deployment Flow](#deployment-flow)

## Project Architecture Overview (Updated for Next.js 15 App Router)

EduPro follows a **Clean Architecture** approach with clear separation of concerns and proper Next.js 15 App Router structure:

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  App Router     │  │  Reusable       │  │  UI Hooks    │ │
│  │  Pages & Routes │  │  Components     │  │  & Utilities │ │
│  │  (_components/) │  │  (components/)  │  │  (hooks/)    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    BUSINESS LOGIC LAYER                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Service Classes│  │  Custom Hooks   │  │ Validation   │ │
│  │  (Core Logic)   │  │  (State Mgmt)   │  │  Services    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    DATA ACCESS LAYER                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Repositories   │  │  API Routes     │  │  Supabase    │ │
│  │  (Data Access)  │  │  (Server Logic) │  │  Client      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    EXTERNAL SERVICES                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Supabase DB    │  │  Supabase Auth  │  │  Supabase    │ │
│  │  (PostgreSQL)   │  │  (User Mgmt)    │  │  Storage     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 🏗️ **Key Architectural Principles**

1. **Route-Specific Components**: Components specific to a route are placed in `app/route/_components/`
2. **Reusable Components**: Truly reusable components are in `src/components/` organized by domain
3. **Clean Separation**: Clear boundaries between presentation, business logic, and data access
4. **Dependency Injection**: Services use dependency injection for better testability
5. **Type Safety**: Full TypeScript coverage with generated database types

## 🔄 **Project Restructuring (December 2024)**

### **What Changed**

The project was comprehensively restructured to eliminate redundancies and establish proper separation of concerns following Next.js 15 App Router best practices:

#### **Before Restructuring:**
- ❌ Duplicate dashboard implementations in `src/app/dashboard/` and `src/components/dashboard/`
- ❌ Route-specific components incorrectly placed in the components folder
- ❌ Complex routing logic in layout files
- ❌ Mixing of reusable and route-specific components

#### **After Restructuring:**
- ✅ Route-specific components moved to `src/app/dashboard/_components/`
- ✅ Only truly reusable components remain in `src/components/`
- ✅ Simplified dashboard layout following App Router conventions
- ✅ Clear separation between presentation, business logic, and data layers
- ✅ Organized UI components in `src/components/ui/` for base components

### **Migration Benefits**

1. **Performance**: Faster builds and better tree-shaking
2. **Maintainability**: Clear separation of concerns
3. **Developer Experience**: Easier to find and modify components
4. **Scalability**: Better structure for adding new features
5. **Best Practices**: Follows Next.js 15 App Router conventions

### **New App Router Module Structure**

The project now follows a **module-based App Router structure** where each major feature has its own dedicated route:

#### **Module Routes Created:**
- **`/dashboard`** - Main dashboard with overview and navigation
- **`/student-management`** - Complete student management system
- **`/staff-management`** - Staff and teacher management
- **`/academic-management`** - Curriculum and academic planning
- **`/attendance-management`** - Student and staff attendance tracking
- **`/fee-management`** - Fee collection and financial management

#### **Shared Layout System:**
- **`AppLayout`** - Main layout wrapper used by all modules
- **`AppHeader`** - Consistent header across all routes
- **`AppSidebar`** - Navigation sidebar with route-aware highlighting
- **`AppContent`** - Content wrapper with proper spacing
- **`AppFooter`** - Optional footer component

#### **Component Organization Rules:**
- **`src/app/route/_components/`**: Components specific to that route only
- **`src/components/layout/`**: Shared layout components used across modules
- **`src/components/ui/`**: Base UI components (Button, Card, Input)
- **`src/components/common/`**: Shared utility components
- **`src/components/auth/`**: Authentication-related components

#### **Benefits of Module Structure:**
1. **Clear Separation**: Each module is self-contained
2. **Better Performance**: Route-level code splitting
3. **Easier Navigation**: Direct URLs for each module
4. **Scalable**: Easy to add new modules
5. **Maintainable**: Clear ownership of components

## Directory Structure Deep Dive

### 📁 **Root Level Files**
```
edupro/
├── package.json              # Dependencies and scripts
├── next.config.js            # Next.js configuration
├── tailwind.config.ts        # Tailwind CSS configuration
├── tsconfig.json             # TypeScript configuration
├── .env.example              # Environment variables template
├── README.md                 # Project documentation
├── SUPABASE_INTEGRATION.md   # Supabase setup guide
└── CODE_FLOW.md              # This file
```

### 📁 **src/ Directory Structure (Restructured for Next.js 15 App Router with Module Routes)**

```
src/
├── app/                      # Next.js 15 App Router (Route-level modules)
│   ├── layout.tsx           # Root layout component
│   ├── page.tsx             # Home page
│   ├── globals.css          # Global styles
│   ├── api/                 # API routes
│   │   ├── students/        # Student-related API endpoints
│   │   └── master-data/     # Master data API endpoints
│   ├── auth/                # Authentication pages
│   ├── dashboard/           # Dashboard module route
│   │   ├── layout.tsx       # Dashboard layout (simplified)
│   │   ├── page.tsx         # Dashboard main page
│   │   └── _components/     # Private dashboard components
│   │       └── dashboard-content.tsx
│   ├── student-management/  # Student Management module route
│   │   ├── layout.tsx       # Student management layout
│   │   ├── page.tsx         # Student management main page
│   │   └── _components/     # Private student management components
│   │       ├── student-management.tsx
│   │       ├── student-list.tsx
│   │       ├── add-student-wizard.tsx
│   │       ├── enhanced-enrollment-wizard.tsx
│   │       └── steps/       # Wizard step components
│   ├── staff-management/    # Staff Management module route
│   │   ├── layout.tsx       # Staff management layout
│   │   ├── page.tsx         # Staff management main page
│   │   └── _components/     # Private staff management components
│   │       └── staff-management.tsx
│   ├── academic-management/ # Academic Management module route
│   │   ├── layout.tsx       # Academic management layout
│   │   ├── page.tsx         # Academic management main page
│   │   └── _components/     # Private academic management components
│   │       └── academic-management.tsx
│   ├── attendance-management/ # Attendance Management module route
│   │   ├── layout.tsx       # Attendance management layout
│   │   ├── page.tsx         # Attendance management main page
│   │   └── _components/     # Private attendance management components
│   │       └── attendance-management.tsx
│   ├── fee-management/      # Fee Management module route
│   │   ├── layout.tsx       # Fee management layout
│   │   ├── page.tsx         # Fee management main page
│   │   └── _components/     # Private fee management components
│   │       └── fee-management.tsx
│   ├── product/             # Product pages
│   └── resources/           # Resource pages
├── components/              # Shared reusable components
│   ├── layout/             # Shared layout components
│   │   ├── app-layout.tsx  # Main application layout wrapper
│   │   ├── app-header.tsx  # Shared header component
│   │   ├── app-sidebar.tsx # Shared sidebar component
│   │   ├── app-content.tsx # Content wrapper component
│   │   ├── app-footer.tsx  # Shared footer component
│   │   └── index.ts        # Layout component exports
│   ├── ui/                 # Base UI components (Button, Card, Input, etc.)
│   ├── auth/               # Authentication components (reusable)
│   ├── common/             # Common UI components (PageWrapper, etc.)
│   ├── landing/            # Landing page components (isolated)
│   └── profile/            # Profile components (reusable)
├── hooks/                  # Custom React hooks
├── lib/                    # Utility libraries and configurations
├── repositories/           # Data access layer
├── services/               # Business logic layer
│   ├── core/              # Core services
│   ├── document/          # Document services
│   ├── enrollment/        # Enrollment services
│   └── student/           # Student services
├── types/                  # TypeScript type definitions
├── constants/              # Application constants
├── config/                 # Configuration files
└── styles/                 # Additional stylesheets
```

## Code Flow Patterns

### 🔄 **Request-Response Flow**

```mermaid
graph TD
    A[User Interaction] --> B[React Component]
    B --> C[Custom Hook]
    C --> D[Service Layer]
    D --> E[Repository Layer]
    E --> F[Supabase Client]
    F --> G[Database/Storage]
    
    G --> H[Response Data]
    H --> I[Repository Processing]
    I --> J[Service Processing]
    J --> K[Hook State Update]
    K --> L[Component Re-render]
    L --> M[UI Update]
```

### 🗂️ **Data Flow Architecture**

1. **UI Components** → Trigger actions
2. **Custom Hooks** → Manage state and API calls
3. **Service Layer** → Business logic and validation
4. **Repository Layer** → Data access abstraction
5. **Supabase Client** → Database operations
6. **Database** → Data persistence

## Creating New Features

### 🆕 **Step-by-Step Guide to Add a New Feature**

Let's walk through creating a new "Teacher Management" feature:

#### Step 1: Database Schema (If needed)
```sql
-- Add to Supabase SQL Editor
CREATE TABLE public.teachers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    subject_specialization TEXT,
    qualification TEXT,
    experience_years INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

#### Step 2: Update Type Definitions
**File: `src/types/database.ts`**
```typescript
// Add to existing Database interface
teachers: {
  Row: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
    phone: string | null;
    subject_specialization: string | null;
    qualification: string | null;
    experience_years: number | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
  };
  Insert: {
    id?: string;
    first_name: string;
    last_name: string;
    email: string;
    phone?: string | null;
    subject_specialization?: string | null;
    qualification?: string | null;
    experience_years?: number | null;
    is_active?: boolean;
    created_at?: string;
    updated_at?: string;
  };
  Update: {
    id?: string;
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string | null;
    subject_specialization?: string | null;
    qualification?: string | null;
    experience_years?: number | null;
    is_active?: boolean;
    created_at?: string;
    updated_at?: string;
  };
};
```

#### Step 3: Create Repository
**File: `src/repositories/teacherRepository.ts`**
```typescript
import { BaseRepository } from './baseRepository';
import { Database } from '../types/database';
import { SupabaseClient } from '@supabase/supabase-js';

export type TeacherEntity = Database['public']['Tables']['teachers']['Row'];
export type TeacherInsert = Database['public']['Tables']['teachers']['Insert'];
export type TeacherUpdate = Database['public']['Tables']['teachers']['Update'];

export interface ITeacherRepository {
  findByEmail(email: string): Promise<TeacherEntity | null>;
  findBySubject(subject: string): Promise<TeacherEntity[]>;
  // Add more teacher-specific methods as needed
}

export class TeacherRepository extends BaseRepository<TeacherEntity, TeacherInsert, TeacherUpdate> implements ITeacherRepository {
  constructor(client: SupabaseClient<Database>) {
    super(client, 'teachers');
  }

  async findByEmail(email: string): Promise<TeacherEntity | null> {
    const { data, error } = await this.client
      .from('teachers')
      .select('*')
      .eq('email', email)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    return data;
  }

  async findBySubject(subject: string): Promise<TeacherEntity[]> {
    const { data, error } = await this.client
      .from('teachers')
      .select('*')
      .eq('subject_specialization', subject)
      .eq('is_active', true)
      .order('first_name');

    if (error) throw error;
    return data || [];
  }
}
```

#### Step 4: Create Service Layer
**File: `src/services/teacher/teacherManagementService.ts`**
```typescript
import { BaseService } from '../core/baseService';
import { TeacherRepository, TeacherEntity, TeacherInsert, TeacherUpdate } from '../../repositories/teacherRepository';
import { ErrorHandler } from '../core/errorHandler';

export class TeacherManagementService extends BaseService {
  constructor(private teacherRepository: TeacherRepository) {
    super('TeacherManagementService');
  }

  async createTeacher(data: TeacherInsert): Promise<TeacherEntity> {
    try {
      // Validate required fields
      if (!data.first_name || !data.last_name || !data.email) {
        throw new Error('First name, last name, and email are required');
      }

      // Check if email already exists
      const existingTeacher = await this.teacherRepository.findByEmail(data.email);
      if (existingTeacher) {
        throw new Error('Teacher with this email already exists');
      }

      // Create teacher
      const teacher = await this.teacherRepository.create(data);
      
      this.log('info', 'Teacher created successfully', { teacherId: teacher.id });
      return teacher;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Failed to create teacher');
    }
  }

  async updateTeacher(id: string, data: TeacherUpdate): Promise<TeacherEntity> {
    try {
      const teacher = await this.teacherRepository.update(id, data);
      this.log('info', 'Teacher updated successfully', { teacherId: id });
      return teacher;
    } catch (error) {
      throw ErrorHandler.handle(error, 'Failed to update teacher');
    }
  }

  async getTeachersBySubject(subject: string): Promise<TeacherEntity[]> {
    try {
      return await this.teacherRepository.findBySubject(subject);
    } catch (error) {
      throw ErrorHandler.handle(error, 'Failed to fetch teachers by subject');
    }
  }
}
```

#### Step 5: Create Custom Hook
**File: `src/hooks/useTeachers.ts`**
```typescript
import { useState, useEffect } from 'react';
import { TeacherEntity, TeacherInsert, TeacherUpdate } from '../repositories/teacherRepository';
import { ServiceFactory } from '../services/serviceFactory';

export function useTeachers() {
  const [teachers, setTeachers] = useState<TeacherEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const teacherService = ServiceFactory.getTeacherManagementService();

  const fetchTeachers = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await teacherService.getAllTeachers();
      setTeachers(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch teachers');
    } finally {
      setLoading(false);
    }
  };

  const createTeacher = async (data: TeacherInsert) => {
    setLoading(true);
    setError(null);
    try {
      const newTeacher = await teacherService.createTeacher(data);
      setTeachers(prev => [...prev, newTeacher]);
      return newTeacher;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create teacher';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const updateTeacher = async (id: string, data: TeacherUpdate) => {
    setLoading(true);
    setError(null);
    try {
      const updatedTeacher = await teacherService.updateTeacher(id, data);
      setTeachers(prev => prev.map(t => t.id === id ? updatedTeacher : t));
      return updatedTeacher;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update teacher';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeachers();
  }, []);

  return {
    teachers,
    loading,
    error,
    createTeacher,
    updateTeacher,
    refreshTeachers: fetchTeachers,
  };
}
```

#### Step 6: Create UI Components
**File: `src/components/teacher-management/teacher-list.tsx`**
```typescript
'use client';

import { useTeachers } from '../../hooks/useTeachers';
import { TeacherEntity } from '../../repositories/teacherRepository';

export function TeacherList() {
  const { teachers, loading, error } = useTeachers();

  if (loading) return <div>Loading teachers...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Teachers</h3>
        <div className="grid gap-4">
          {teachers.map((teacher) => (
            <TeacherCard key={teacher.id} teacher={teacher} />
          ))}
        </div>
      </div>
    </div>
  );
}

function TeacherCard({ teacher }: { teacher: TeacherEntity }) {
  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="flex justify-between items-start">
        <div>
          <h4 className="text-sm font-medium text-gray-900">
            {teacher.first_name} {teacher.last_name}
          </h4>
          <p className="text-sm text-gray-500">{teacher.email}</p>
          {teacher.subject_specialization && (
            <p className="text-sm text-gray-500">
              Subject: {teacher.subject_specialization}
            </p>
          )}
        </div>
        <div className="flex space-x-2">
          <button className="text-indigo-600 hover:text-indigo-900">
            Edit
          </button>
          <button className="text-red-600 hover:text-red-900">
            Delete
          </button>
        </div>
      </div>
    </div>
  );
}
```

#### Step 7: Create Page
**File: `src/app/teachers/page.tsx`**
```typescript
import { TeacherList } from '../../components/teacher-management/teacher-list';
import { PageWrapper } from '../../components/common/page-wrapper';

export default function TeachersPage() {
  return (
    <PageWrapper title="Teacher Management">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-bold text-gray-900">Teachers</h1>
          <button className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
            Add Teacher
          </button>
        </div>
        <TeacherList />
      </div>
    </PageWrapper>
  );
}
```

#### Step 8: Add API Route (if needed)
**File: `src/app/api/teachers/route.ts`**
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { ServiceFactory } from '../../../services/serviceFactory';

export async function GET(request: NextRequest) {
  try {
    const teacherService = ServiceFactory.getTeacherManagementService();
    const teachers = await teacherService.getAllTeachers();
    
    return NextResponse.json({ data: teachers, success: true });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch teachers', success: false },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const teacherService = ServiceFactory.getTeacherManagementService();
    const teacher = await teacherService.createTeacher(body);
    
    return NextResponse.json({ data: teacher, success: true });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Failed to create teacher', success: false },
      { status: 500 }
    );
  }
}
```

#### Step 9: Update Service Factory
**File: `src/services/serviceFactory.ts`**
```typescript
// Add teacher service to existing factory
import { TeacherManagementService } from './teacher/teacherManagementService';
import { TeacherRepository } from '../repositories/teacherRepository';

export class ServiceFactory {
  // ...existing code...

  static getTeacherManagementService(): TeacherManagementService {
    const teacherRepository = new TeacherRepository(supabase);
    return new TeacherManagementService(teacherRepository);
  }

  // ...existing code...
}
```

## Modifying Existing Features

### 🔧 **Adding a Field to Student Entity**

#### Step 1: Update Database Schema
```sql
-- Add new column to existing table
ALTER TABLE public.students ADD COLUMN emergency_contact TEXT;
```

#### Step 2: Update Types
**File: `src/types/database.ts`**
```typescript
// Add emergency_contact to student Row, Insert, and Update types
students: {
  Row: {
    // ...existing fields...
    emergency_contact: string | null;
  };
  Insert: {
    // ...existing fields...
    emergency_contact?: string | null;
  };
  Update: {
    // ...existing fields...
    emergency_contact?: string | null;
  };
};
```

#### Step 3: Update Repository (if needed)
**File: `src/repositories/studentRepository.ts`**
```typescript
// Add methods if special handling is needed
async findByEmergencyContact(contact: string): Promise<StudentEntity[]> {
  const { data, error } = await this.client
    .from('students')
    .select('*')
    .eq('emergency_contact', contact);
  
  if (error) throw error;
  return data || [];
}
```

#### Step 4: Update Service Layer
**File: `src/services/studentManagementService.ts`**
```typescript
// Update validation logic if needed
async createStudent(data: StudentInsert): Promise<StudentEntity> {
  // Add validation for emergency_contact if required
  if (data.emergency_contact && !this.isValidPhone(data.emergency_contact)) {
    throw new Error('Invalid emergency contact number');
  }
  
  // ...existing code...
}
```

#### Step 5: Update UI Components
**File: `src/components/student-management/add-student-wizard.tsx`**
```typescript
// Add form field for emergency contact
<div>
  <label className="block text-sm font-medium text-gray-700">
    Emergency Contact
  </label>
  <input
    type="tel"
    value={formData.emergency_contact || ''}
    onChange={(e) => setFormData({
      ...formData,
      emergency_contact: e.target.value
    })}
    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm"
  />
</div>
```

## Database Operations Flow

### 🗄️ **Data Flow Pattern**

```typescript
// Standard CRUD operations flow
Component → Hook → Service → Repository → Supabase → Database

// Example: Creating a new student
StudentForm → useStudents → StudentManagementService → StudentRepository → supabase → PostgreSQL
```

### 📊 **Repository Pattern Implementation**

```typescript
// Base Repository provides common operations
class BaseRepository<T, TInsert, TUpdate> {
  // Common methods: findById, findAll, create, update, delete
}

// Specific repositories extend base functionality
class StudentRepository extends BaseRepository {
  // Student-specific methods: findByEmail, findByRollNumber, etc.
}
```

### 🔄 **Service Layer Responsibilities**

```typescript
// Service layer handles:
// 1. Business logic validation
// 2. Data transformation
// 3. Error handling
// 4. Logging
// 5. Transaction management
```

## UI Component Flow (Updated Architecture)

### 🎨 **Component Hierarchy (Restructured)**

```
Page Component (src/app/*/page.tsx)
├── Layout Component (src/app/layout.tsx or src/app/*/layout.tsx)
├── Route-Specific Components (src/app/*/_components/)
│   ├── Private components for that route only
│   ├── Not accessible from other routes
│   └── Co-located with the pages that use them
├── Reusable Components (src/components/)
│   ├── UI Components (src/components/ui/)
│   │   ├── Button, Card, Input (base components)
│   │   └── Consistent design system
│   ├── Domain Components (src/components/domain/)
│   │   ├── Student Management (*-list.tsx, *-form.tsx)
│   │   ├── Profile Components
│   │   └── Authentication Components
│   └── Common Components (src/components/common/)
│       ├── PageWrapper, LoadingSpinner
│       └── Shared utility components
```

### 🏗️ **Component Organization Rules**

1. **Route-Specific**: Use `_components/` folder within app routes
2. **Reusable**: Place in `src/components/` organized by domain
3. **UI Base**: Core design system components in `src/components/ui/`
4. **Common**: Shared utilities in `src/components/common/`

### 🔧 **Component Creation Guidelines**

1. **Naming Convention**: Use descriptive names (e.g., `StudentEnrollmentForm`)
2. **File Structure**: One component per file
3. **Props Interface**: Always define TypeScript interfaces for props
4. **State Management**: Use custom hooks for complex state
5. **Error Handling**: Implement proper error boundaries

```typescript
// Component template
interface ComponentProps {
  // Define props interface
}

export function ComponentName({ prop1, prop2 }: ComponentProps) {
  // Component logic
  return (
    // JSX
  );
}
```

## State Management Flow

### 📊 **State Management Patterns**

```typescript
// 1. Local State (useState)
const [data, setData] = useState<DataType[]>([]);

// 2. Custom Hooks (Complex state + API calls)
const { data, loading, error, actions } = useCustomHook();

// 3. Context (Global state)
const context = useContext(AppContext);

// 4. URL State (Search params, routing)
const searchParams = useSearchParams();
```

### 🔄 **Custom Hook Pattern**

```typescript
// Standard custom hook structure
export function useFeature() {
  const [state, setState] = useState(initialState);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const performAction = async (data: ActionData) => {
    setLoading(true);
    setError(null);
    try {
      // Perform action
      const result = await service.performAction(data);
      setState(result);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    state,
    loading,
    error,
    actions: {
      performAction,
    },
  };
}
```

## File Organization Guidelines

### 📁 **Naming Conventions**

- **Files**: kebab-case (`student-management.tsx`)
- **Components**: PascalCase (`StudentManagement`)
- **Functions**: camelCase (`createStudent`)
- **Constants**: UPPER_SNAKE_CASE (`DATABASE_TABLES`)
- **Types**: PascalCase (`StudentEntity`)

### 🗂️ **File Structure Rules**

1. **Group by Feature**: Related files should be grouped together
2. **Separation of Concerns**: Each file should have a single responsibility
3. **Index Files**: Use index.ts for clean imports
4. **Barrel Exports**: Export commonly used items from index files

```typescript
// src/components/student-management/index.ts
export { StudentList } from './student-list';
export { StudentForm } from './student-form';
export { StudentCard } from './student-card';

// Usage
import { StudentList, StudentForm } from '../components/student-management';
```

### 📋 **Import Organization**

```typescript
// 1. External libraries
import React from 'react';
import { NextPage } from 'next';

// 2. Internal utilities and configurations
import { cn } from '../lib/utils';
import { config } from '../lib/config';

// 3. Types and interfaces
import { StudentEntity } from '../types/database';

// 4. Hooks and services
import { useStudents } from '../hooks/useStudents';

// 5. Components
import { Button } from '../components/common/button';
```

## Common Development Patterns

### 🔄 **Error Handling Pattern**

```typescript
// Consistent error handling across the application
try {
  const result = await someOperation();
  return result;
} catch (error) {
  // Log error
  console.error('Operation failed:', error);
  
  // Handle specific error types
  if (error.code === 'SPECIFIC_ERROR') {
    throw new Error('User-friendly message');
  }
  
  // Generic error handling
  throw ErrorHandler.handle(error, 'Operation failed');
}
```

### 🎯 **Form Handling Pattern**

```typescript
// Standard form handling with validation
export function FormComponent() {
  const [formData, setFormData] = useState<FormData>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    // Validation logic
    if (!formData.field1) {
      newErrors.field1 = 'Field 1 is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    try {
      await submitForm(formData);
      // Handle success
    } catch (error) {
      // Handle error
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
}
```

### 📱 **Responsive Design Pattern**

```typescript
// Consistent responsive design approach
<div className="
  grid 
  grid-cols-1 
  md:grid-cols-2 
  lg:grid-cols-3 
  gap-4 
  p-4 
  sm:p-6 
  lg:p-8
">
  {/* Content */}
</div>
```

## Testing Approach

### 🧪 **Testing Strategy**

1. **Unit Tests**: Test individual functions and components
2. **Integration Tests**: Test component interactions
3. **E2E Tests**: Test complete user workflows
4. **API Tests**: Test API endpoints

### 📁 **Test File Organization**

```
src/
├── components/
│   ├── student-management/
│   │   ├── student-list.tsx
│   │   └── student-list.test.tsx
├── services/
│   ├── studentManagementService.ts
│   └── studentManagementService.test.ts
└── __tests__/
    ├── integration/
    ├── e2e/
    └── utils/
```

### 🔍 **Testing Patterns**

```typescript
// Component testing pattern
import { render, screen, fireEvent } from '@testing-library/react';
import { StudentList } from './student-list';

describe('StudentList', () => {
  it('renders student list correctly', () => {
    render(<StudentList />);
    expect(screen.getByText('Students')).toBeInTheDocument();
  });

  it('handles loading state', () => {
    render(<StudentList loading={true} />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });
});
```

## Deployment Flow

### 🚀 **Build and Deployment Process**

```bash
# Development
npm run dev

# Build for production
npm run build

# Start production server
npm run start

# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

### 🔧 **Environment Configuration**

```bash
# Development (.env.local)
NEXT_PUBLIC_SUPABASE_URL=your-dev-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-dev-key

# Production (.env.production)
NEXT_PUBLIC_SUPABASE_URL=your-prod-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-prod-key
```

### 📊 **Deployment Checklist**

- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Tests passing
- [ ] Build successful
- [ ] Performance optimized
- [ ] Security headers configured
- [ ] Monitoring setup

## Quick Reference Commands

### 📝 **Common Development Tasks**

```bash
# Start development server
npm run dev

# Generate Supabase types
npx supabase gen types typescript --project-id your-project-id > src/types/database.ts

# Run database migrations
npx supabase db push

# Reset database
npx supabase db reset

# Run tests
npm test

# Format code
npm run format

# Check types
npm run type-check
```

### 🔗 **Useful File Paths**

- **Add new page**: `src/app/your-page/page.tsx`
- **Add new component**: `src/components/feature/component-name.tsx`
- **Add new hook**: `src/hooks/useFeatureName.ts`
- **Add new service**: `src/services/feature/featureService.ts`
- **Add new repository**: `src/repositories/featureRepository.ts`
- **Add API route**: `src/app/api/feature/route.ts`
- **Update types**: `src/types/database.ts`
- **Add constants**: `src/constants/database.ts`

---

## 🎯 **Key Takeaways for New Developers**

1. **Follow the Architecture**: Always use the Repository → Service → Hook → Component pattern
2. **Type Safety**: Use TypeScript interfaces and generated types
3. **Error Handling**: Implement consistent error handling at all levels
4. **Code Organization**: Group related files together and use clear naming conventions
5. **Testing**: Write tests for critical functionality
6. **Performance**: Optimize for performance and user experience
7. **Documentation**: Document complex logic and maintain this guide

This guide should provide you with everything needed to understand and work with the EduPro codebase effectively. For specific questions or clarifications, refer to the other documentation files or reach out to the development team.
