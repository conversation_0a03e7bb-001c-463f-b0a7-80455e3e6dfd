// src/hooks/useMasterData.ts
import { useEffect, useState } from 'react';
import { MasterDataService } from '../services/masterDataService';
import type { AcademicYear, Class, GuardianRelation, Section } from '../types/database';

interface MasterData {
  classes: Class[];
  sections: Section[];
  academicYears: AcademicYear[];
  guardianRelations: GuardianRelation[];
  currentAcademicYear: AcademicYear | null;
}

interface UseMasterDataReturn {
  data: MasterData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useMasterData = (): UseMasterDataReturn => {
  const [data, setData] = useState<MasterData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get empty data structure
  const getEmptyData = (): MasterData => {
    return {
      classes: [],
      sections: [],
      academicYears: [],
      guardianRelations: [],
      currentAcademicYear: null
    };
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching master data...');

      // Add timeout to prevent infinite loading
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Master data fetch timeout')), 5000); // 5 second timeout
      });

      const masterData = await Promise.race([
        MasterDataService.getAllMasterData(),
        timeoutPromise
      ]);

      console.log('✅ Master data fetched successfully:', {
        classes: masterData.classes?.length || 0,
        sections: masterData.sections?.length || 0,
        academicYears: masterData.academicYears?.length || 0,
        guardianRelations: masterData.guardianRelations?.length || 0,
        currentAcademicYear: masterData.currentAcademicYear?.year || 'None'
      });

      setData(masterData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('❌ Error fetching master data:', err);
      setError(errorMessage);

      // Use empty data on error
      const emptyData = getEmptyData();
      setData(emptyData);
      console.log('🔄 Using empty master data due to error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Start with empty data and fetch real data
    const emptyData = getEmptyData();
    setData(emptyData);
    console.log('🚀 Starting with empty master data');

    // Fetch real data
    fetchData();
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
};

// Individual hooks for specific master data
export const useClasses = () => {
  const [classes, setClasses] = useState<Class[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClasses = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await MasterDataService.getClasses();
        setClasses(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch classes';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchClasses();
  }, []);

  return { classes, loading, error };
};

export const useSections = () => {
  const [sections, setSections] = useState<Section[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSections = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await MasterDataService.getSections();
        setSections(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch sections';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchSections();
  }, []);

  return { sections, loading, error };
};

export const useAcademicYears = () => {
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAcademicYears = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await MasterDataService.getAcademicYears();
        setAcademicYears(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch academic years';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchAcademicYears();
  }, []);

  return { academicYears, loading, error };
};

export const useGuardianRelations = () => {
  const [guardianRelations, setGuardianRelations] = useState<GuardianRelation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGuardianRelations = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await MasterDataService.getGuardianRelations();
        setGuardianRelations(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch guardian relations';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchGuardianRelations();
  }, []);

  return { guardianRelations, loading, error };
};
