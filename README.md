# EduPro - Modern School Management System

A comprehensive school management system built with **Next.js 15**, **TypeScript**, **Supabase**, and **Tailwind CSS**. EduPro follows clean architecture principles with a repository pattern, modular services, and type-safe database operations.

## 🚀 Features

### Core Features
- **Student Enrollment Management**: Complete multi-step enrollment wizard with validation
- **Student & Guardian Management**: Comprehensive CRUD operations with relationships
- **Academic Records**: Class, section, and academic year management
- **Document Management**: File upload, validation, and storage with Supabase Storage
- **Master Data Management**: Centralized management of all reference data
- **Authentication**: Secure user authentication with Supabase Auth
- **Real-time Validation**: Live form validation and uniqueness checking

### Technical Features
- **Clean Architecture**: Repository pattern with service layer abstraction
- **Type Safety**: Full TypeScript implementation with strict typing
- **Centralized Configuration**: Single source of truth for database schema
- **Modular Services**: Business logic separated into focused service classes
- **Error Handling**: Comprehensive error handling with custom error types
- **Responsive Design**: Modern UI with Tailwind CSS
- **Performance Optimized**: Built with Next.js 15 App Router and Turbo

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Next.js 15    │  │   React Hooks   │  │  Tailwind    │ │
│  │   App Router    │  │   Components    │  │     CSS      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                   Service Layer                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │ Student Mgmt    │  │  Document Mgmt  │  │ Enrollment   │ │
│  │   Service       │  │    Service      │  │ Mgmt Service │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Repository Layer                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Student       │  │   Guardian      │  │   Document   │ │
│  │  Repository     │  │  Repository     │  │  Repository  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 Database Layer                              │
│               Supabase PostgreSQL                           │
│          Authentication + Storage + RLS                     │
└─────────────────────────────────────────────────────────────┘
```

## 📦 Tech Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript 5.7** - Type safety and developer experience
- **Tailwind CSS 3.4** - Utility-first CSS framework
- **React Hook Form 7.5** - Form handling and validation

### Backend & Database
- **Supabase** - Backend-as-a-Service
  - PostgreSQL Database
  - Authentication
  - Real-time subscriptions
  - File Storage
  - Row Level Security (RLS)

### Development
- **ESLint** - Code linting
- **Prettier** - Code formatting
- **TypeScript** - Static type checking
- **Turbo** - Fast development builds

## 🛠️ Installation & Setup

### Prerequisites
- **Node.js 18+** 
- **npm/yarn/pnpm**
- **Supabase account**

### 1. Clone & Install

```bash
git clone <repository-url>
cd edupro
npm install
```

### 2. Environment Setup

Create `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Optional: Additional Configuration
NEXT_PUBLIC_APP_NAME=EduPro
NEXT_PUBLIC_APP_VERSION=1.0.0
```

### 3. Database Setup

See [`SUPABASE_INTEGRATION.md`](./SUPABASE_INTEGRATION.md) for detailed database setup instructions.

### 4. Start Development

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🗄️ Project Structure

```
src/
├── app/                          # Next.js 15 App Router
│   ├── auth/                     # Authentication pages
│   ├── dashboard/                # Dashboard pages
│   ├── api/                      # API routes
│   │   ├── master-data/
│   │   └── students/
│   ├── layout.tsx                # Root layout
│   └── page.tsx                  # Home page
├── components/                   # React components
│   ├── auth/                     # Authentication components
│   ├── common/                   # Shared components
│   ├── dashboard/                # Dashboard components
│   ├── landing/                  # Landing page components
│   ├── profile/                  # Profile components
│   └── student-management/       # Student management components
│       ├── add-student-wizard.tsx
│       ├── enhanced-enrollment-wizard.tsx
│       └── steps/                # Wizard steps
├── constants/                    # Constants and configuration
│   └── database.ts               # Database schema definitions
├── hooks/                        # Custom React hooks
│   ├── useMasterData.ts
│   └── useStudents.ts
├── lib/                         # Utility libraries
│   ├── auth.ts
│   ├── supabase.ts
│   └── utils.ts
├── repositories/                # Data access layer
│   ├── baseRepository.ts
│   ├── studentRepository.ts
│   ├── guardianRepository.ts
│   ├── academicRecordRepository.ts
│   └── documentRepository.ts
├── services/                    # Business logic layer
│   ├── core/                    # Core services
│   │   ├── baseService.ts
│   │   ├── databaseService.ts
│   │   └── errorHandler.ts
│   ├── document/                # Document services
│   │   ├── documentService.ts
│   │   └── storageService.ts
│   ├── enrollment/              # Enrollment services
│   │   └── validationService.ts
│   ├── student/                 # Legacy student services
│   ├── studentManagementService.ts
│   ├── enrollmentManagementService.ts
│   └── documentManagementService.ts
├── styles/                      # CSS styles
└── types/                       # TypeScript type definitions
    └── database.ts              # Database types
```

## 🔧 Core Architecture Components

### 1. Repository Pattern

Repositories handle all database operations with type safety:

```typescript
// Example: StudentRepository
import { StudentRepository } from '@/repositories/studentRepository';

const studentRepo = new StudentRepository(supabaseClient);
const students = await studentRepo.findAll();
const student = await studentRepo.findById(id);
```

### 2. Service Layer

Services contain business logic and orchestrate repository calls:

```typescript
// Example: StudentManagementService
import { StudentManagementService } from '@/services/studentManagementService';

const service = new StudentManagementService(supabaseClient);
const result = await service.createCompleteStudent({
  student: studentData,
  guardian: guardianData,
  academicRecord: academicData
});
```

### 3. Centralized Database Configuration

All database schema is defined in one place:

```typescript
// src/constants/database.ts
export const DATABASE_TABLES = {
  STUDENTS: 'students',
  GUARDIANS: 'guardians',
  ACADEMIC_RECORDS: 'academic_records',
  // ... more tables
} as const;

export const DATABASE_COLUMNS = {
  STUDENTS: {
    ID: 'id',
    FIRST_NAME: 'first_name',
    LAST_NAME: 'last_name',
    // ... more columns
  }
} as const;
```

### 4. Type-Safe Database Operations

All operations are fully typed using generated Supabase types:

```typescript
// Fully typed operations
const student: StudentEntity = await repository.create(studentData);
const students: StudentEntity[] = await repository.findAll(filters);
```

## 🎯 Usage Examples

### Creating a New Student

```typescript
import { StudentManagementService } from '@/services/studentManagementService';

const service = new StudentManagementService(supabaseClient);

const newStudent = await service.createCompleteStudent({
  student: {
    first_name: 'John',
    last_name: 'Doe',
    date_of_birth: '2010-05-15',
    gender: 'male',
    email: '<EMAIL>'
  },
  guardian: {
    name: 'Jane Doe',
    relation_id: 'parent-relation-id',
    phone: '+1234567890',
    email: '<EMAIL>'
  },
  academicRecord: {
    class_id: 'class-id',
    section_id: 'section-id',
    academic_year_id: 'year-id',
    roll_number: '2024001',
    admission_date: '2024-06-01'
  }
});
```

### Using Master Data

```typescript
import { useMasterData } from '@/hooks/useMasterData';

function StudentForm() {
  const { 
    classes, 
    sections, 
    academicYears, 
    guardianRelations,
    loading,
    error 
  } = useMasterData();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <form>
      <select name="classId">
        {classes.map(cls => (
          <option key={cls.id} value={cls.id}>{cls.name}</option>
        ))}
      </select>
      {/* More form fields */}
    </form>
  );
}
```

### Document Management

```typescript
import { DocumentManagementService } from '@/services/documentManagementService';

const docService = new DocumentManagementService(supabaseClient);

// Upload a document
const result = await docService.uploadDocument({
  file: selectedFile,
  type: 'birth_certificate',
  studentId: 'student-id',
  isRequired: true
});

// Get student documents
const documents = await docService.getStudentDocuments('student-id');
```

## � Authentication

The application uses Supabase Auth with custom components:

```typescript
import { AuthProvider } from '@/components/auth/auth-provider';
import { useAuth } from '@/components/auth/auth-provider';

// Wrap your app
<AuthProvider>
  <App />
</AuthProvider>

// Use in components
function Dashboard() {
  const { user, loading, signOut } = useAuth();
  // Component logic
}
```

## 📝 Scripts

```bash
# Development
npm run dev          # Start development server with Turbo
npm run build        # Build for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
npm run type-check   # TypeScript type checking
```

## 🧪 Testing Database Connection

```typescript
// test-connection.js
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function testConnection() {
  try {
    const { data, error } = await supabase
      .from('students')
      .select('count(*)')
      .single();
    
    if (error) throw error;
    console.log('✅ Database connection successful');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  }
}

testConnection();
```

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Follow the existing architecture patterns:
   - Use repositories for data access
   - Implement business logic in services
   - Maintain type safety throughout
4. Update documentation if needed
5. Run tests: `npm run type-check && npm run lint`
6. Commit: `git commit -m 'Add amazing feature'`
7. Push: `git push origin feature/amazing-feature`
8. Create a Pull Request

## 📚 Documentation

- [Supabase Integration Guide](./SUPABASE_INTEGRATION.md) - Detailed setup and usage
- [Architecture Guide](./docs/ARCHITECTURE.md) - Deep dive into system design
- [API Documentation](./docs/API.md) - API endpoints and usage

## 🐛 Troubleshooting

### Common Issues

1. **Environment Variables**: Ensure all Supabase keys are correctly set
2. **Database Permissions**: Check Row Level Security (RLS) policies
3. **Type Errors**: Run `npm run type-check` to identify issues
4. **Build Errors**: Check for unused imports and variables

### Getting Help

- Check the [Supabase Integration Guide](./SUPABASE_INTEGRATION.md)
- Review error messages in browser console
- Verify database schema matches expected structure
- Ensure environment variables are properly configured

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ using Next.js 15, TypeScript, Supabase, and modern web technologies**

### 2. Environment Setup

Create a `.env` file in the root directory:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
SUPABASE_PROJECT_ID=your_project_id
```

### 3. Database Setup

The application expects the following tables in your Supabase database:

```sql
-- Core Tables (create these in your Supabase SQL editor)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  role TEXT CHECK (role IN ('student', 'teacher', 'admin', 'parent')) DEFAULT 'student',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE classes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE sections (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE academic_years (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  year TEXT NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  is_current BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE guardian_relations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE students (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  date_of_birth DATE NOT NULL,
  gender TEXT CHECK (gender IN ('male', 'female', 'other')) NOT NULL,
  email TEXT,
  phone_number TEXT,
  address TEXT,
  guardian_name TEXT NOT NULL,
  guardian_relation_id UUID REFERENCES guardian_relations(id),
  guardian_phone TEXT NOT NULL,
  guardian_email TEXT,
  guardian_address TEXT,
  emergency_contact TEXT,
  class_id UUID REFERENCES classes(id),
  section_id UUID REFERENCES sections(id),
  roll_number TEXT NOT NULL,
  previous_school TEXT,
  academic_year_id UUID REFERENCES academic_years(id),
  profile_id UUID REFERENCES profiles(id),
  birth_certificate_url TEXT,
  previous_records_url TEXT,
  medical_records_url TEXT,
  photograph_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(roll_number, class_id, section_id, academic_year_id)
);
```

### 4. Start Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 🗄️ Database Configuration System

### Overview

EduPro uses a centralized database configuration system that allows you to change table names across the entire application by updating a single configuration file.

### Configuration File: `src/config/database.ts`

```typescript
export const DATABASE_TABLES = {
  // Master Tables
  CLASSES: 'classes',
  SECTIONS: 'sections', 
  ACADEMIC_YEARS: 'academic_years',
  GUARDIAN_RELATIONS: 'guardian_relations',
  
  // Student Tables
  STUDENTS: 'students',
  
  // Future Tables (ready for expansion)
  TEACHERS: 'teachers',
  SUBJECTS: 'subjects',
  // ... more tables
} as const;
```

### How to Change Table Names

#### Step 1: Update Configuration

Edit `src/config/database.ts`:

```typescript
export const DATABASE_TABLES = {
  CLASSES: 'school_classes',        // Changed from 'classes'
  SECTIONS: 'class_sections',       // Changed from 'sections'
  STUDENTS: 'enrolled_students',    // Changed from 'students'
  // ... other tables
} as const;
```

#### Step 2: Restart Application

```bash
npm run dev
```

That's it! The entire application now uses your new table names.

### Usage in Code

#### ✅ Correct Usage (Recommended)

```typescript
import { DATABASE_TABLES } from '../config/database';

// In service classes
const { data } = await supabase
  .from(DATABASE_TABLES.STUDENTS)
  .select('*');

// With destructuring
import { STUDENTS, CLASSES } from '../config/database';
const studentsData = await supabase.from(STUDENTS).select('*');
```

#### ❌ Incorrect Usage (Avoid)

```typescript
// Don't hardcode table names
const { data } = await supabase
  .from('students')  // ❌ Hardcoded
  .select('*');
```

## 🔧 Service Layer Architecture

### Master Data Service: `src/services/masterDataService.ts`

Handles all master data operations (classes, sections, academic years, guardian relations).

```typescript
import { MasterDataService } from '../services/masterDataService';

// Get all classes
const classes = await MasterDataService.getClasses();

// Get current academic year
const currentYear = await MasterDataService.getCurrentAcademicYear();

// Get all master data at once
const masterData = await MasterDataService.getAllMasterData();
```

### Student Service: `src/services/studentService.ts`

Handles all student-related operations.

```typescript
import { StudentService } from '../services/studentService';

// Create a new student
const newStudent = await StudentService.createStudent({
  firstName: 'John',
  lastName: 'Doe',
  dateOfBirth: '2010-05-15',
  gender: 'male',
  guardianName: 'Jane Doe',
  guardianRelationId: 'parent-id',
  guardianPhone: '+1234567890',
  classId: 'class-id',
  sectionId: 'section-id',
  rollNumber: '2024001',
  academicYearId: 'year-id'
});

// Get all students with relations
const students = await StudentService.getAllStudents();

// Search students
const searchResults = await StudentService.searchStudents('John');

// Update student
const updated = await StudentService.updateStudent('student-id', {
  firstName: 'Johnny'
});

// Delete student (soft delete)
await StudentService.deleteStudent('student-id');
```

## 🎣 React Hooks

### useMasterData Hook: `src/hooks/useMasterData.ts`

```typescript
import { useMasterData } from '../hooks/useMasterData';

function MyComponent() {
  const { 
    data: masterData, 
    loading, 
    error,
    classes,
    sections,
    academicYears,
    guardianRelations,
    currentAcademicYear
  } = useMasterData();

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <select>
        {classes.map(cls => (
          <option key={cls.id} value={cls.id}>{cls.name}</option>
        ))}
      </select>
    </div>
  );
}
```

### useStudents Hook: `src/hooks/useStudents.ts`

```typescript
import { useStudents } from '../hooks/useStudents';

function StudentManagement() {
  const { createStudent, loading, error } = useStudents();

  const handleCreateStudent = async (studentData) => {
    try {
      await createStudent(studentData);
      alert('Student created successfully!');
    } catch (err) {
      console.error('Failed to create student:', err);
    }
  };

  return (
    <form onSubmit={handleCreateStudent}>
      {/* Form fields */}
    </form>
  );
}
```

## 🌐 API Routes

### Master Data API: `/api/master-data`

```typescript
// Get all master data
fetch('/api/master-data')

// Get specific data type
fetch('/api/master-data?type=classes')
fetch('/api/master-data?type=sections')
fetch('/api/master-data?type=academic-years')
fetch('/api/master-data?type=guardian-relations')
fetch('/api/master-data?type=current-academic-year')
```

### Students API: `/api/students`

```typescript
// Create student
fetch('/api/students', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(studentData)
})

// Get all students
fetch('/api/students')

// Search students
fetch('/api/students?search=john')

// Get students by class and section
fetch('/api/students?classId=xxx&sectionId=yyy')
```

### Roll Number Validation: `/api/students/check-roll-number`

```typescript
// Check if roll number is available
fetch('/api/students/check-roll-number', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    rollNumber: '2024001',
    classId: 'class-id',
    sectionId: 'section-id',
    academicYearId: 'year-id'
  })
})
```

## 🎨 UI Components

### Add Student Wizard: `src/components/student-management/add-student-wizard.tsx`

A comprehensive multi-step form for student enrollment with:

- Real-time roll number validation
- Dynamic dropdowns from Supabase master tables
- Form validation and error handling
- File upload support (planned)

```typescript
import AddStudentWizard from '../components/student-management/add-student-wizard';

function StudentManagement() {
  const [showWizard, setShowWizard] = useState(false);

  return (
    <div>
      <button onClick={() => setShowWizard(true)}>
        Add New Student
      </button>
      
      {showWizard && (
        <AddStudentWizard
          onClose={() => setShowWizard(false)}
          onSuccess={(message) => alert(message)}
          onError={(error) => alert(error)}
        />
      )}
    </div>
  );
}
```

## 🔒 Authentication

### Setup Authentication Provider

```typescript
// src/app/layout.tsx
import { AuthProvider } from '../components/auth/auth-provider';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
```

### Use Authentication in Components

```typescript
import { useAuth } from '../components/auth/auth-provider';

function Dashboard() {
  const { user, loading, signOut } = useAuth();

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>Please sign in</div>;

  return (
    <div>
      <h1>Welcome, {user.email}</h1>
      <button onClick={signOut}>Sign Out</button>
    </div>
  );
}
```

## 🔧 Development Workflow

### Adding New Tables

1. **Update Database Configuration**:
```typescript
// src/config/database.ts
export const DATABASE_TABLES = {
  // ...existing tables
  TEACHERS: 'teachers',        // Add new table
  SUBJECTS: 'subjects',        // Add new table
} as const;
```

2. **Update Database Types**:
```typescript
// src/types/database.ts
export interface Database {
  public: {
    Tables: {
      // ...existing tables
      teachers: {
        Row: {
          id: string;
          name: string;
          // ...other fields
        };
        Insert: {
          // ...insert fields
        };
        Update: {
          // ...update fields
        };
      };
    };
  };
}
```

3. **Create Service Class**:
```typescript
// src/services/teacherService.ts
import { DATABASE_TABLES } from '../config/database';

export class TeacherService {
  static async getAllTeachers() {
    return await supabase
      .from(DATABASE_TABLES.TEACHERS)
      .select('*');
  }
}
```

4. **Create React Hook**:
```typescript
// src/hooks/useTeachers.ts
export const useTeachers = () => {
  // Implementation
};
```

5. **Add API Route**:
```typescript
// src/app/api/teachers/route.ts
export async function GET() {
  // Implementation
}
```

### Testing Changes

```bash
# Build and check for errors
npm run build

# Run development server
npm run dev

# Test database connection
node -e "
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY);
supabase.from('classes').select('*').limit(1).then(console.log);
"
```

## 🐛 Troubleshooting

### Common Issues

1. **Environment Variables**: Ensure all Supabase environment variables are set correctly
2. **Database Permissions**: Check RLS policies in Supabase
3. **Table Names**: Verify table names match your configuration
4. **TypeScript Errors**: Run `npm run build` to check for type issues

### Database Connection Test

```typescript
// test-connection.js
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

async function testConnection() {
  try {
    const { data, error } = await supabase.from('classes').select('*').limit(1);
    if (error) throw error;
    console.log('✅ Database connection successful:', data);
  } catch (error) {
    console.error('❌ Database connection failed:', error);
  }
}

testConnection();
```

## 📱 Deployment

### Environment Variables for Production

```env
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key
```

### Build and Deploy

```bash
# Build for production
npm run build

# Start production server
npm start
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch: `git checkout -b feature/amazing-feature`
3. Update the database configuration if adding new tables
4. Follow the service layer pattern for new features
5. Add proper TypeScript types
6. Test your changes: `npm run build`
7. Commit your changes: `git commit -m 'Add amazing feature'`
8. Push to the branch: `git push origin feature/amazing-feature`
9. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Check the troubleshooting section above
- Review the database configuration documentation
- Ensure all environment variables are properly set
- Verify Supabase table structure matches the expected schema

---

**Built with ❤️ using Next.js, TypeScript, and Supabase**
