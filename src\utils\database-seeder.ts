// src/utils/database-seeder.ts
/**
 * Database seeding utility for populating master data tables
 * This helps ensure the dropdown fields have data to display
 */

import { supabase } from '../lib/supabase';
import { DATABASE_TABLES } from '../constants/database';

/**
 * Sample data for seeding the database
 */
const SEED_DATA = {
  classes: [
    { name: 'Nursery', description: 'Pre-school nursery class', grade_level: 0, is_active: true },
    { name: 'LKG', description: 'Lower Kindergarten', grade_level: 1, is_active: true },
    { name: 'UKG', description: 'Upper Kindergarten', grade_level: 2, is_active: true },
    { name: 'Class 1', description: 'First standard', grade_level: 3, is_active: true },
    { name: 'Class 2', description: 'Second standard', grade_level: 4, is_active: true },
    { name: 'Class 3', description: 'Third standard', grade_level: 5, is_active: true },
    { name: 'Class 4', description: 'Fourth standard', grade_level: 6, is_active: true },
    { name: 'Class 5', description: 'Fifth standard', grade_level: 7, is_active: true },
    { name: 'Class 6', description: 'Sixth standard', grade_level: 8, is_active: true },
    { name: 'Class 7', description: 'Seventh standard', grade_level: 9, is_active: true },
    { name: 'Class 8', description: 'Eighth standard', grade_level: 10, is_active: true },
    { name: 'Class 9', description: 'Ninth standard', grade_level: 11, is_active: true },
    { name: 'Class 10', description: 'Tenth standard', grade_level: 12, is_active: true }
  ],

  sections: [
    { name: 'A', max_capacity: 30, is_active: true },
    { name: 'B', max_capacity: 30, is_active: true },
    { name: 'C', max_capacity: 30, is_active: true },
    { name: 'D', max_capacity: 25, is_active: true }
  ],

  academicYears: [
    { 
      year: '2023-24', 
      start_date: '2023-04-01', 
      end_date: '2024-03-31', 
      is_active: false 
    },
    { 
      year: '2024-25', 
      start_date: '2024-04-01', 
      end_date: '2025-03-31', 
      is_active: true 
    },
    { 
      year: '2025-26', 
      start_date: '2025-04-01', 
      end_date: '2026-03-31', 
      is_active: false 
    }
  ],

  guardianRelations: [
    { name: 'Father', is_active: true },
    { name: 'Mother', is_active: true },
    { name: 'Guardian', is_active: true },
    { name: 'Grandfather', is_active: true },
    { name: 'Grandmother', is_active: true },
    { name: 'Uncle', is_active: true },
    { name: 'Aunt', is_active: true },
    { name: 'Brother', is_active: true },
    { name: 'Sister', is_active: true },
    { name: 'Other', is_active: true }
  ]
};

/**
 * Seed classes table
 */
export async function seedClasses(): Promise<boolean> {
  try {
    console.log('🌱 Seeding classes table...');
    
    // Check if data already exists
    const { data: existing } = await supabase
      .from(DATABASE_TABLES.CLASSES)
      .select('id')
      .limit(1);

    if (existing && existing.length > 0) {
      console.log('ℹ️  Classes table already has data, skipping seed');
      return true;
    }

    const { error } = await supabase
      .from(DATABASE_TABLES.CLASSES)
      .insert(SEED_DATA.classes);

    if (error) {
      console.error('❌ Error seeding classes:', error);
      return false;
    }

    console.log(`✅ Successfully seeded ${SEED_DATA.classes.length} classes`);
    return true;
  } catch (error) {
    console.error('❌ Error seeding classes:', error);
    return false;
  }
}

/**
 * Seed sections table
 */
export async function seedSections(): Promise<boolean> {
  try {
    console.log('🌱 Seeding sections table...');
    
    // Check if data already exists
    const { data: existing } = await supabase
      .from(DATABASE_TABLES.SECTIONS)
      .select('id')
      .limit(1);

    if (existing && existing.length > 0) {
      console.log('ℹ️  Sections table already has data, skipping seed');
      return true;
    }

    const { error } = await supabase
      .from(DATABASE_TABLES.SECTIONS)
      .insert(SEED_DATA.sections);

    if (error) {
      console.error('❌ Error seeding sections:', error);
      return false;
    }

    console.log(`✅ Successfully seeded ${SEED_DATA.sections.length} sections`);
    return true;
  } catch (error) {
    console.error('❌ Error seeding sections:', error);
    return false;
  }
}

/**
 * Seed academic years table
 */
export async function seedAcademicYears(): Promise<boolean> {
  try {
    console.log('🌱 Seeding academic years table...');
    
    // Check if data already exists
    const { data: existing } = await supabase
      .from(DATABASE_TABLES.ACADEMIC_YEARS)
      .select('id')
      .limit(1);

    if (existing && existing.length > 0) {
      console.log('ℹ️  Academic years table already has data, skipping seed');
      return true;
    }

    const { error } = await supabase
      .from(DATABASE_TABLES.ACADEMIC_YEARS)
      .insert(SEED_DATA.academicYears);

    if (error) {
      console.error('❌ Error seeding academic years:', error);
      return false;
    }

    console.log(`✅ Successfully seeded ${SEED_DATA.academicYears.length} academic years`);
    return true;
  } catch (error) {
    console.error('❌ Error seeding academic years:', error);
    return false;
  }
}

/**
 * Seed guardian relations table
 */
export async function seedGuardianRelations(): Promise<boolean> {
  try {
    console.log('🌱 Seeding guardian relations table...');
    
    // Check if data already exists
    const { data: existing } = await supabase
      .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
      .select('id')
      .limit(1);

    if (existing && existing.length > 0) {
      console.log('ℹ️  Guardian relations table already has data, skipping seed');
      return true;
    }

    const { error } = await supabase
      .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
      .insert(SEED_DATA.guardianRelations);

    if (error) {
      console.error('❌ Error seeding guardian relations:', error);
      return false;
    }

    console.log(`✅ Successfully seeded ${SEED_DATA.guardianRelations.length} guardian relations`);
    return true;
  } catch (error) {
    console.error('❌ Error seeding guardian relations:', error);
    return false;
  }
}

/**
 * Seed all master data tables
 */
export async function seedAllMasterData(): Promise<{
  success: boolean;
  results: {
    classes: boolean;
    sections: boolean;
    academicYears: boolean;
    guardianRelations: boolean;
  };
}> {
  console.log('🚀 Starting master data seeding...');
  
  const results = {
    classes: await seedClasses(),
    sections: await seedSections(),
    academicYears: await seedAcademicYears(),
    guardianRelations: await seedGuardianRelations()
  };

  const success = Object.values(results).every(result => result);
  
  console.log('📊 Seeding results:', results);
  console.log(success ? '✅ All master data seeded successfully!' : '❌ Some seeding operations failed');
  
  return { success, results };
}

/**
 * Clear all master data (use with caution!)
 */
export async function clearAllMasterData(): Promise<boolean> {
  try {
    console.log('🗑️  Clearing all master data...');
    
    const tables = [
      DATABASE_TABLES.GUARDIAN_RELATIONS,
      DATABASE_TABLES.ACADEMIC_YEARS,
      DATABASE_TABLES.SECTIONS,
      DATABASE_TABLES.CLASSES
    ];

    for (const table of tables) {
      const { error } = await supabase
        .from(table)
        .delete()
        .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records

      if (error) {
        console.error(`❌ Error clearing ${table}:`, error);
        return false;
      }
      
      console.log(`✅ Cleared ${table}`);
    }

    console.log('✅ All master data cleared successfully');
    return true;
  } catch (error) {
    console.error('❌ Error clearing master data:', error);
    return false;
  }
}
