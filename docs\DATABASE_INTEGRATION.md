# Database Integration Guide

## Overview

The EduPro student enrollment wizard is fully integrated with Supabase database to dynamically populate dropdown fields. This document explains the implementation, configuration, and troubleshooting.

## Architecture

### 1. Centralized Database Configuration

All database table names and column names are centralized in `src/constants/database.ts`:

```typescript
export const DATABASE_TABLES = {
  CLASSES: 'classes',
  SECTIONS: 'sections', 
  ACADEMIC_YEARS: 'academic_years',
  GUARDIAN_RELATIONS: 'guardian_relations',
  STUDENTS: 'students'
};

export const DATABASE_COLUMNS = {
  CLASSES: {
    ID: 'id',
    NAME: 'name',
    DESCRIPTION: 'description',
    IS_ACTIVE: 'is_active'
  },
  // ... other table columns
};
```

### 2. Service Layer Architecture

The system follows clean architecture principles:

- **Repository Layer**: Direct Supabase queries in `MasterDataService`
- **Service Layer**: Business logic and data transformation
- **Hook Layer**: React hooks for component integration (`useMasterData`)
- **Component Layer**: UI components that consume the data

### 3. Data Flow

```
Supabase Database → MasterDataService → useMasterData Hook → Wizard Components → Dropdown Fields
```

## Implementation Details

### Master Data Service

Located in `src/services/masterDataService.ts`, this service:

- ✅ Uses centralized `DATABASE_TABLES` constants
- ✅ Returns empty arrays when data fetch fails (no hardcoded fallbacks)
- ✅ Implements proper error handling with detailed logging
- ✅ Uses `Promise.allSettled` for graceful failure handling

### Student Enrollment Wizard

Located in `src/app/student-management/_components/add-student-wizard.tsx`:

- ✅ Dynamically populates dropdowns from database
- ✅ Shows empty dropdowns when data fetch fails
- ✅ Provides user feedback for empty states
- ✅ Maintains all existing functionality

### Dropdown Fields Integration

The following dropdowns are dynamically populated:

1. **Classes** - From `classes` table
2. **Sections** - From `sections` table  
3. **Academic Years** - From `academic_years` table
4. **Guardian Relations** - From `guardian_relations` table

## Database Schema Requirements

### Required Tables

```sql
-- Classes table
CREATE TABLE classes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  description TEXT,
  grade_level INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Sections table  
CREATE TABLE sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  class_id UUID REFERENCES classes(id),
  max_capacity INTEGER DEFAULT 30,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Academic Years table
CREATE TABLE academic_years (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  year VARCHAR NOT NULL,
  start_date DATE,
  end_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Guardian Relations table
CREATE TABLE guardian_relations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## Testing and Verification

### Database Test Page

Access `/test-db` to verify database connectivity and table structure:

1. **Run Database Tests** - Verifies connection and table existence
2. **Seed Sample Data** - Populates tables with sample data if empty
3. **View Console Logs** - Detailed debugging information

### Manual Testing

1. Navigate to Student Management → Add Student
2. Check that all dropdowns load data from database
3. Verify empty states show appropriate messages
4. Test form submission with selected values

## Troubleshooting

### Common Issues

#### 1. Empty Dropdowns

**Symptoms**: All dropdowns show "No [items] available"

**Causes & Solutions**:
- **Missing Tables**: Run database verification at `/test-db`
- **Empty Tables**: Use "Seed Sample Data" button to populate
- **Permission Issues**: Check Supabase RLS policies
- **Connection Issues**: Verify Supabase credentials in `.env.local`

#### 2. Console Errors

**Check browser console for**:
- Database connection errors
- Table permission errors  
- Query syntax errors
- Network connectivity issues

#### 3. Incorrect Table Names

**Symptoms**: "Table does not exist" errors

**Solution**: Verify table names in `DATABASE_TABLES` constant match your Supabase schema

### Debug Steps

1. **Open Browser Console** (F12)
2. **Navigate to Student Management**
3. **Check for error messages** in console
4. **Run database tests** at `/test-db`
5. **Verify Supabase connection** in project settings

## Configuration Management

### Environment Variables

Required in `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### Database Constants

To change table or column names:

1. Update `src/constants/database.ts`
2. Ensure Supabase schema matches
3. Test with `/test-db` page
4. Update any hardcoded references

## Error Handling Strategy

The system implements a **graceful degradation** strategy:

1. **Primary**: Fetch data from Supabase
2. **Fallback**: Show empty dropdowns with helpful messages
3. **User Feedback**: Clear error messages and guidance
4. **No Hardcoded Data**: Never show fake/fallback data

This ensures users always see the true state of the database and are guided to resolve issues.

## Best Practices

1. **Always use centralized constants** for table/column names
2. **Handle errors gracefully** with empty states
3. **Provide clear user feedback** for empty/error states
4. **Log detailed information** for debugging
5. **Test database connectivity** before deployment
6. **Use proper TypeScript types** for data structures
7. **Follow clean architecture** patterns for maintainability

## Future Enhancements

- Real-time updates using Supabase subscriptions
- Caching layer for improved performance
- Advanced filtering and search capabilities
- Bulk data operations
- Data validation and constraints
