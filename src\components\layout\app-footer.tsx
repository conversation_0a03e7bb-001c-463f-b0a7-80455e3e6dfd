// src/components/layout/app-footer.tsx
'use client';

const AppFooter = () => {
  return (
    <footer className="bg-white border-t border-gray-200 px-6 py-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <p className="text-sm text-gray-600">
            © 2024 EduPro. All rights reserved.
          </p>
        </div>
        
        <div className="flex items-center space-x-6">
          <a href="#" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
            Help
          </a>
          <a href="#" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
            Privacy
          </a>
          <a href="#" className="text-sm text-gray-600 hover:text-gray-900 transition-colors">
            Terms
          </a>
          <div className="text-sm text-gray-500">
            Version 2.0.0
          </div>
        </div>
      </div>
    </footer>
  );
};

export default AppFooter;
