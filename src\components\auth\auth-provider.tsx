// src/components/auth/auth-provider.tsx
'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { User } from '../../lib/auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error: string | null }>;
  signUp: (email: string, password: string, userData: { name: string; role: string }) => Promise<{ success: boolean; error: string | null }>;
  signOut: () => Promise<void>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;
    let subscription: any = null;
    let timeoutId: NodeJS.Timeout;

    // Force loading to false after maximum timeout to prevent infinite loading
    const forceLoadingComplete = () => {
      if (isMounted) {
        setLoading(false);
        setUser(null);
        setError(null);
      }
    };

    const initializeAuth = async () => {
      try {
        // Set reasonable timeout for session verification
        timeoutId = setTimeout(forceLoadingComplete, 3000);

        // Check for existing session first (faster than Supabase call)
        const { verifySession } = await import('../../lib/session');
        const sessionUser = await verifySession();

        if (sessionUser && isMounted) {
          clearTimeout(timeoutId);
          setUser(sessionUser);
          setError(null);
          setLoading(false);
          return;
        }

        // If no session, check Supabase as fallback
        const { getCurrentUser } = await import('../../lib/supabase-auth-functions');
        const currentUser = await getCurrentUser();

        if (currentUser && isMounted) {
          // Create session for existing Supabase user
          const { createSession } = await import('../../lib/session');
          await createSession(currentUser);
          setUser(currentUser);
        }

        if (isMounted) {
          clearTimeout(timeoutId);
          setError(null);
          setLoading(false);
        }
      } catch (_error) {
        if (isMounted) {
          clearTimeout(timeoutId);
          setUser(null);
          setError(null);
          setLoading(false);
        }
      }
    };

    // Set up auth state listener with error handling
    const setupAuthListener = async () => {
      try {
        const { onAuthStateChange } = await import('../../lib/supabase-auth-functions');

        const { data } = await onAuthStateChange((user) => {
          if (isMounted) {
            setUser(user);
            if (loading) {
              setLoading(false);
            }
          }
        });
        subscription = data?.subscription;
      } catch (_error) {
        // Silently fail and ensure loading is false
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Start initialization
    initializeAuth();
    setupAuthListener();

    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      subscription?.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      const { signIn: authSignIn } = await import('../../lib/supabase-auth-functions');
      const result = await authSignIn(email, password);
      if (result.success && result.user) {
        // Create secure session via server action
        const { createSession } = await import('../../lib/session');
        await createSession(result.user);
        setUser(result.user);
      }
      return { success: result.success, error: result.error };
    } catch (_error) {
      return { success: false, error: 'Authentication service unavailable' };
    }
  };

  const signUp = async (email: string, password: string, userData: { name: string; role: string }) => {
    try {
      const { signUp: authSignUp } = await import('../../lib/supabase-auth-functions');
      const result = await authSignUp(email, password, userData);
      if (result.success && result.user) {
        // Create secure session via server action
        const { createSession } = await import('../../lib/session');
        await createSession(result.user);
        setUser(result.user);
      }
      return { success: result.success, error: result.error };
    } catch (_error) {
      return { success: false, error: 'Authentication service unavailable' };
    }
  };

  const signOut = async () => {
    try {
      const { signOut: authSignOut } = await import('../../lib/supabase-auth-functions');
      const { deleteSession } = await import('../../lib/session');

      // Sign out from Supabase
      await authSignOut();

      // Delete secure session
      await deleteSession();

      setUser(null);
      setError(null);
    } catch (error) {
      console.error('Sign out error:', error);
      // Force sign out locally even if service fails
      const { deleteSession } = await import('../../lib/session');
      await deleteSession();
      setUser(null);
      setError(null);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
