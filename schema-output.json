{"tables": {"profiles": {"columns": []}, "students": {"columns": []}, "classes": {"columns": []}, "sections": {"columns": []}, "academic_years": {"columns": [{"name": "id", "type": "string", "nullable": true, "default": null}, {"name": "tenant_id", "type": "string", "nullable": true, "default": null}, {"name": "name", "type": "string", "nullable": true, "default": null}, {"name": "start_date", "type": "string", "nullable": true, "default": null}, {"name": "end_date", "type": "string", "nullable": true, "default": null}, {"name": "is_current", "type": "boolean", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": null}, {"name": "created_at", "type": "string", "nullable": true, "default": null}, {"name": "updated_at", "type": "string", "nullable": true, "default": null}]}, "guardian_relations": {"columns": []}, "guardians": {"columns": []}, "documents": {"columns": []}, "academic_records": {"columns": []}, "enrollments": {"columns": []}, "teachers": {"columns": []}, "subjects": {"columns": []}, "attendance": {"columns": []}, "grades": {"columns": []}}, "tableGroups": {"core": ["students", "guardians", "documents", "academic_records"], "master_data": ["classes", "sections", "academic_years", "guardian_relations"], "system": [], "auth": ["profiles"], "future": ["enrollments", "teachers", "subjects", "attendance", "grades"]}}