// src/lib/client-services.tsx
'use client';

/**
 * Client-side service provider for React components
 * This provides services to client components with proper React context
 */

import { createBrowserClient } from '@supabase/ssr';
import { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { Database } from '../types/database';

// Service imports
import { DocumentManagementService } from '../services/documentManagementService';
import { ValidationService } from '../services/enrollment/validationService';
import { EnrollmentManagementService } from '../services/enrollmentManagementService';
import { StudentManagementService } from '../services/studentManagementService';

/**
 * Client-side service container
 */
export interface ClientServiceContainer {
  studentManagementService: StudentManagementService;
  enrollmentManagementService: EnrollmentManagementService;
  documentManagementService: DocumentManagementService;
  validationService: ValidationService;
  isInitialized: boolean;
  error: string | null;
}

/**
 * Service context
 */
const ServiceContext = createContext<ClientServiceContainer | null>(null);

/**
 * Service provider props
 */
interface ServiceProviderProps {
  children: ReactNode;
}

/**
 * Service provider component
 * Provides services to all child components
 */
export function ServiceProvider({ children }: ServiceProviderProps) {
  const [services, setServices] = useState<ClientServiceContainer>({
    studentManagementService: null as any,
    enrollmentManagementService: null as any,
    documentManagementService: null as any,
    validationService: null as any,
    isInitialized: false,
    error: null,
  });

  useEffect(() => {
    const initializeServices = async () => {
      try {
        console.log('Initializing client-side services...');

        // Create Supabase client for client components
        const supabase = createBrowserClient<Database>(
          process.env.NEXT_PUBLIC_SUPABASE_URL!,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
        );

        // Initialize services
        const studentManagementService = new StudentManagementService(supabase);
        const enrollmentManagementService = new EnrollmentManagementService(supabase);
        const documentManagementService = new DocumentManagementService(supabase);
        const validationService = new ValidationService(supabase);

        setServices({
          studentManagementService,
          enrollmentManagementService,
          documentManagementService,
          validationService,
          isInitialized: true,
          error: null,
        });

        console.log('✓ Client-side services initialized successfully');
      } catch (error) {
        console.error('Failed to initialize client-side services:', error);
        setServices(prev => ({
          ...prev,
          error: error instanceof Error ? error.message : 'Failed to initialize services',
        }));
      }
    };

    initializeServices();
  }, []);

  return (
    <ServiceContext.Provider value={services}>
      {children}
    </ServiceContext.Provider>
  );
}

/**
 * Hook to use services in client components
 */
export function useServices(): ClientServiceContainer {
  const context = useContext(ServiceContext);
  
  if (!context) {
    throw new Error('useServices must be used within a ServiceProvider');
  }
  
  return context;
}

/**
 * Hook to use student management service
 */
export function useStudentManagementService(): StudentManagementService {
  const { studentManagementService, isInitialized, error } = useServices();
  
  if (error) {
    throw new Error(`Service initialization failed: ${error}`);
  }
  
  if (!isInitialized) {
    throw new Error('Services are not yet initialized');
  }
  
  return studentManagementService;
}

/**
 * Hook to use enrollment management service
 */
export function useEnrollmentManagementService(): EnrollmentManagementService {
  const { enrollmentManagementService, isInitialized, error } = useServices();
  
  if (error) {
    throw new Error(`Service initialization failed: ${error}`);
  }
  
  if (!isInitialized) {
    throw new Error('Services are not yet initialized');
  }
  
  return enrollmentManagementService;
}

/**
 * Hook to use document management service
 */
export function useDocumentManagementService(): DocumentManagementService {
  const { documentManagementService, isInitialized, error } = useServices();
  
  if (error) {
    throw new Error(`Service initialization failed: ${error}`);
  }
  
  if (!isInitialized) {
    throw new Error('Services are not yet initialized');
  }
  
  return documentManagementService;
}

/**
 * Hook to use validation service
 */
export function useValidationService(): ValidationService {
  const { validationService, isInitialized, error } = useServices();
  
  if (error) {
    throw new Error(`Service initialization failed: ${error}`);
  }
  
  if (!isInitialized) {
    throw new Error('Services are not yet initialized');
  }
  
  return validationService;
}

/**
 * Hook for service operations with loading and error states
 */
export function useServiceOperation<T>() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T | null>(null);

  const execute = async (operation: () => Promise<T>) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await operation();
      setData(result);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Operation failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const reset = () => {
    setLoading(false);
    setError(null);
    setData(null);
  };

  return {
    loading,
    error,
    data,
    execute,
    reset,
  };
}

/**
 * Hook for paginated service operations
 */
export function usePaginatedServiceOperation<T>() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<T[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 20,
    totalCount: 0,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false,
  });

  const execute = async (
    operation: (page: number, pageSize: number) => Promise<{
      data: T[];
      pagination: typeof pagination;
    }>
  ) => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await operation(pagination.page, pagination.pageSize);
      setData(result.data);
      setPagination(result.pagination);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Operation failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const nextPage = () => {
    if (pagination.hasNextPage) {
      setPagination(prev => ({ ...prev, page: prev.page + 1 }));
    }
  };

  const previousPage = () => {
    if (pagination.hasPreviousPage) {
      setPagination(prev => ({ ...prev, page: prev.page - 1 }));
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= pagination.totalPages) {
      setPagination(prev => ({ ...prev, page }));
    }
  };

  const changePageSize = (pageSize: number) => {
    setPagination(prev => ({ ...prev, pageSize, page: 1 }));
  };

  const reset = () => {
    setLoading(false);
    setError(null);
    setData([]);
    setPagination({
      page: 1,
      pageSize: 20,
      totalCount: 0,
      totalPages: 0,
      hasNextPage: false,
      hasPreviousPage: false,
    });
  };

  return {
    loading,
    error,
    data,
    pagination,
    execute,
    nextPage,
    previousPage,
    goToPage,
    changePageSize,
    reset,
  };
}

/**
 * Service loading component
 */
export function ServiceLoadingBoundary({ children }: { children: ReactNode }) {
  const { isInitialized, error } = useServices();

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center max-w-md">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Service Error</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing services...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
