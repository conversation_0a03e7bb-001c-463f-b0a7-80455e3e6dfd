// src/app/attendance-management/layout.tsx
import { Metadata } from 'next';
import AppLayout from '@/components/layout/app-layout';

export const metadata: Metadata = {
  title: 'Attendance Management - EduPro',
  description: 'Track and manage student and staff attendance records',
};

interface AttendanceManagementLayoutProps {
  children: React.ReactNode;
}

export default function AttendanceManagementLayout({ children }: AttendanceManagementLayoutProps) {
  return (
    <AppLayout>
      {children}
    </AppLayout>
  );
}
