// src/components/layout/app-content.tsx
'use client';

interface AppContentProps {
  children: React.ReactNode;
  className?: string;
  showQuote?: boolean;
}

const InspirationalQuote = () => {
  return (
    <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-700 rounded-xl p-3 shadow-xl shadow-indigo-300/30 border border-indigo-200/20 relative overflow-hidden mt-6">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
      <div className="absolute top-0 right-0 w-20 h-20 bg-white/5 rounded-full -translate-y-10 translate-x-10"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-white/5 rounded-full translate-y-8 -translate-x-8"></div>

      {/* Content */}
      <div className="relative z-10">
        <div className="flex items-start space-x-2">
          <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
            </svg>
          </div>
          <div className="flex-1">
            <blockquote className="text-white/95 text-sm font-medium leading-relaxed mb-2">
              "Education is the most powerful weapon which you can use to change the world. Every school is a beacon of hope, shaping minds that will build a better tomorrow for our nation."
            </blockquote>

            {/* Author - Right Aligned */}
            <div className="flex items-center justify-between">
              <p className="text-white/80 text-sm flex items-center">
                🌟 Building tomorrow's leaders, one student at a time
              </p>
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">NM</span>
                </div>
                <div className="text-right">
                  <p className="text-white/90 text-sm font-semibold">Nelson Mandela</p>
                  <p className="text-white/70 text-sm">Former President of South Africa</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AppContent = ({ children, className = '', showQuote = false }: AppContentProps) => {
  return (
    <main className={`h-full overflow-y-auto bg-gray-100 p-6 ${className}`}>
      <div className="min-h-full">
        {children}
        {showQuote && <InspirationalQuote />}
      </div>
    </main>
  );
};

export default AppContent;
