// src/components/student-management/enhanced-enrollment-wizard.tsx
'use client';

import React, { useCallback, useEffect, useState } from 'react';
import { ErrorCode, ServiceError } from '../../../services/core/errorHandler';
import {
    EnrollmentProgress,
    EnrollmentSession,
    EnrollmentStep
} from '../../../services/enrollmentManagementService';
import { getService } from '../../../services/serviceFactory';

// Step components
import AcademicInfoStep from './steps/academic-info-step';
import DocumentsStep from './steps/documents-step';
import GuardianDetailsStep from './steps/guardian-details-step';
import PersonalInfoStep from './steps/personal-info-step';
import ReviewStep from './steps/review-step';

/**
 * Enhanced enrollment wizard with service integration
 */
interface EnhancedEnrollmentWizardProps {
  onComplete?: (result: any) => void;
  onCancel?: () => void;
  sessionId?: string; // For resuming existing session
}

const EnhancedEnrollmentWizard: React.FC<EnhancedEnrollmentWizardProps> = ({
  onComplete,
  onCancel,
  sessionId: initialSessionId
}) => {
  // State management
  const [enrollmentService, setEnrollmentService] = useState<any | null>(null);
  const [session, setSession] = useState<EnrollmentSession | null>(null);
  const [progress, setProgress] = useState<EnrollmentProgress | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string[]>>({});

  // Initialize service and session
  useEffect(() => {
    const initializeWizard = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get enrollment management service
        const service = await getService('enrollmentManagementService');
        setEnrollmentService(service);

        let currentSession: EnrollmentSession;

        if (initialSessionId) {
          // Resume existing session
          try {
            currentSession = await service.resumeEnrollment(initialSessionId);
          } catch (error) {
            if (error instanceof ServiceError && error.code === ErrorCode.SESSION_EXPIRED) {
              // Session expired, start new one
              currentSession = await service.startEnrollment();
            } else {
              throw error;
            }
          }
        } else {
          // Start new session
          currentSession = await service.startEnrollment();
        }

        setSession(currentSession);

        // Get progress
        const currentProgress = await service.getEnrollmentProgress(currentSession.id);
        setProgress(currentProgress);

        setValidationErrors(currentSession.validationErrors);

      } catch (error) {
        console.error('Failed to initialize enrollment wizard:', error);
        setError(error instanceof Error ? error.message : 'Failed to initialize enrollment');
      } finally {
        setLoading(false);
      }
    };

    initializeWizard();
  }, [initialSessionId]);

  // Handle step completion
  const handleStepComplete = useCallback(async (step: EnrollmentStep, stepData: any) => {
    if (!enrollmentService || !session) return;

    try {
      setSubmitting(true);
      setError(null);

      // Update enrollment step
      const updatedSession = await enrollmentService.updateEnrollmentStep(
        session.id,
        step,
        stepData
      );

      setSession(updatedSession);

      // Update progress
      const updatedProgress = await enrollmentService.getEnrollmentProgress(updatedSession.id);
      setProgress(updatedProgress);

      // Clear validation errors for this step
      setValidationErrors(prev => ({
        ...prev,
        [step]: []
      }));

    } catch (error) {
      console.error('Failed to update enrollment step:', error);
      
      if (error instanceof ServiceError && error.code === ErrorCode.VALIDATION_ERROR) {
        // Handle validation errors
        setValidationErrors(prev => ({
          ...prev,
          [step]: Array.isArray(error.originalError) ? error.originalError : [error.message]
        }));
      } else {
        setError(error instanceof Error ? error.message : 'Failed to update step');
      }
    } finally {
      setSubmitting(false);
    }
  }, [enrollmentService, session]);

  // Handle enrollment completion
  const handleComplete = useCallback(async () => {
    if (!enrollmentService || !session) return;

    try {
      setSubmitting(true);
      setError(null);

      // Complete enrollment
      const result = await enrollmentService.completeEnrollment(session.id);

      if (result.success) {
        onComplete?.(result);
      } else {
        setError(result.errors?.join(', ') || 'Enrollment failed');
      }

    } catch (error) {
      console.error('Failed to complete enrollment:', error);
      setError(error instanceof Error ? error.message : 'Failed to complete enrollment');
    } finally {
      setSubmitting(false);
    }
  }, [enrollmentService, session, onComplete]);

  // Handle cancellation
  const handleCancel = useCallback(async () => {
    if (enrollmentService && session) {
      try {
        await enrollmentService.cancelEnrollment(session.id);
      } catch (error) {
        console.warn('Failed to cancel enrollment session:', error);
      }
    }
    onCancel?.();
  }, [enrollmentService, session, onCancel]);

  // Handle step navigation
  const handleStepNavigation = useCallback((targetStep: EnrollmentStep) => {
    if (!session) return;

    // Update session current step (for navigation)
    setSession(prev => prev ? { ...prev, currentStep: targetStep } : null);
  }, [session]);

  // Render loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Initializing enrollment wizard...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error && !session) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center max-w-md">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6">
            <div className="flex items-center justify-center w-12 h-12 bg-red-100 rounded-full mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Initialization Failed</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!session || !progress) {
    return null;
  }

  // Render current step
  const renderCurrentStep = () => {
    const stepProps = {
      data: session.data,
      validationErrors: validationErrors[session.currentStep] || [],
      onComplete: (stepData: any) => handleStepComplete(session.currentStep, stepData),
      onBack: () => {
        const steps = [EnrollmentStep.PERSONAL_INFO, EnrollmentStep.GUARDIAN_DETAILS, EnrollmentStep.ACADEMIC_INFO, EnrollmentStep.DOCUMENTS];
        const currentIndex = steps.indexOf(session.currentStep);
        if (currentIndex > 0) {
          handleStepNavigation(steps[currentIndex - 1]);
        }
      },
      submitting
    };

    switch (session.currentStep) {
      case EnrollmentStep.PERSONAL_INFO:
        return <PersonalInfoStep {...stepProps} />;
      
      case EnrollmentStep.GUARDIAN_DETAILS:
        return <GuardianDetailsStep {...stepProps} />;
      
      case EnrollmentStep.ACADEMIC_INFO:
        return <AcademicInfoStep {...stepProps} />;
      
      case EnrollmentStep.DOCUMENTS:
        return <DocumentsStep {...stepProps} />;
      
      case EnrollmentStep.COMPLETED:
        return (
          <ReviewStep
            data={session.data}
            onComplete={handleComplete}
            onBack={() => handleStepNavigation(EnrollmentStep.DOCUMENTS)}
            submitting={submitting}
          />
        );
      
      default:
        return <div>Unknown step</div>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Student Enrollment</h1>
        <p className="text-gray-600">Complete all steps to enroll a new student</p>
      </div>

      {/* Progress Bar */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Progress</span>
          <span className="text-sm text-gray-500">{progress.percentage}% Complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress.percentage}%` }}
          ></div>
        </div>
      </div>

      {/* Step Navigation */}
      <div className="mb-8">
        <nav className="flex space-x-4">
          {[
            { step: EnrollmentStep.PERSONAL_INFO, label: 'Personal Info', icon: '👤' },
            { step: EnrollmentStep.GUARDIAN_DETAILS, label: 'Guardian Details', icon: '👨‍👩‍👧‍👦' },
            { step: EnrollmentStep.ACADEMIC_INFO, label: 'Academic Info', icon: '🎓' },
            { step: EnrollmentStep.DOCUMENTS, label: 'Documents', icon: '📄' },
          ].map(({ step, label, icon }) => {
            const isCompleted = progress.completedSteps.includes(step);
            const isCurrent = session.currentStep === step;
            const isAccessible = isCompleted || isCurrent;

            return (
              <button
                key={step}
                onClick={() => isAccessible && handleStepNavigation(step)}
                disabled={!isAccessible}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  isCurrent
                    ? 'bg-blue-100 text-blue-700 border-2 border-blue-300'
                    : isCompleted
                    ? 'bg-green-100 text-green-700 border-2 border-green-300'
                    : 'bg-gray-100 text-gray-400 border-2 border-gray-200 cursor-not-allowed'
                }`}
              >
                <span>{icon}</span>
                <span>{label}</span>
                {isCompleted && (
                  <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Current Step Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {renderCurrentStep()}
      </div>

      {/* Footer Actions */}
      <div className="mt-8 flex justify-between">
        <button
          onClick={handleCancel}
          className="px-6 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          Cancel Enrollment
        </button>
        
        <div className="text-sm text-gray-500">
          Session ID: {session.id.slice(0, 8)}...
        </div>
      </div>
    </div>
  );
};

export default EnhancedEnrollmentWizard;
