// src/app/reports/page.tsx
'use client';

import { useState } from 'react';

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl-app font-bold text-slate-900">Reports & Analytics</h1>
          <p className="text-base-app text-slate-600 mt-1">
            Generate comprehensive reports and view school analytics
          </p>
        </div>
        <button className="btn-primary">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Generate Report
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-slate-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: '📊' },
            { id: 'academic', label: 'Academic Reports', icon: '📚' },
            { id: 'attendance', label: 'Attendance Reports', icon: '📅' },
            { id: 'financial', label: 'Financial Reports', icon: '💰' },
            { id: 'custom', label: 'Custom Reports', icon: '⚙️' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm-app transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h2 className="text-xl-app font-semibold text-slate-900">Reports Dashboard</h2>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Total Reports', value: '156', change: '+12', color: 'blue' },
                { title: 'This Month', value: '24', change: '+8', color: 'green' },
                { title: 'Scheduled', value: '8', change: '+2', color: 'purple' },
                { title: 'Downloads', value: '89', change: '+15', color: 'orange' },
              ].map((stat, index) => (
                <div key={index} className="bg-slate-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm-app font-medium text-slate-600">{stat.title}</p>
                      <p className="text-2xl-app font-bold text-slate-900">{stat.value}</p>
                    </div>
                    <div className={`text-sm-app font-medium ${
                      stat.change.startsWith('+') ? 'text-green-600' : 'text-slate-500'
                    }`}>
                      {stat.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Popular Reports */}
            <div>
              <h3 className="text-lg-app font-medium text-slate-900 mb-4">Popular Reports</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { title: 'Student Enrollment Report', icon: '👨‍🎓', desc: 'Monthly enrollment statistics', downloads: '45' },
                  { title: 'Attendance Summary', icon: '📅', desc: 'Class-wise attendance data', downloads: '38' },
                  { title: 'Fee Collection Report', icon: '💰', desc: 'Financial collection summary', downloads: '32' },
                  { title: 'Academic Performance', icon: '📊', desc: 'Student performance analytics', downloads: '28' },
                  { title: 'Staff Report', icon: '👨‍🏫', desc: 'Staff attendance and details', downloads: '25' },
                  { title: 'Class Schedule', icon: '⏰', desc: 'Timetable and schedule reports', downloads: '22' },
                ].map((report, index) => (
                  <div key={index} className="border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                    <div className="flex items-center justify-between mb-2">
                      <div className="text-2xl">{report.icon}</div>
                      <div className="text-xs-app text-slate-500">{report.downloads} downloads</div>
                    </div>
                    <h4 className="text-base-app font-medium text-slate-900 mb-1">{report.title}</h4>
                    <p className="text-sm-app text-slate-600">{report.desc}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'academic' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Academic Reports</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Generate reports on student performance, grades, and academic progress
            </p>
            <button className="btn-primary">Generate Academic Report</button>
          </div>
        )}

        {activeTab === 'attendance' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📅</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Attendance Reports</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Create detailed attendance reports for students and staff
            </p>
            <button className="btn-primary">Generate Attendance Report</button>
          </div>
        )}

        {activeTab === 'financial' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">💰</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Financial Reports</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Generate comprehensive financial and fee collection reports
            </p>
            <button className="btn-primary">Generate Financial Report</button>
          </div>
        )}

        {activeTab === 'custom' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">⚙️</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Custom Reports</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Create custom reports with specific filters and parameters
            </p>
            <button className="btn-primary">Create Custom Report</button>
          </div>
        )}
      </div>
    </div>
  );
}
