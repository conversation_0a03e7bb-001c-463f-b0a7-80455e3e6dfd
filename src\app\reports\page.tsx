// src/app/reports/page.tsx
'use client';

import { useState } from 'react';

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState('dashboard');

  return (
    <div className="space-y-6">
      {/* Header Section - Compact Design (30vh) */}
      <div className="relative overflow-hidden bg-gradient-to-br from-purple-100 via-pink-50 to-blue-100 rounded-lg p-4 shadow-lg border border-white/50 h-[30vh] min-h-[240px]">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-purple-200/20 via-transparent to-blue-200/20"></div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-pink-200/30 to-transparent rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-blue-200/30 to-transparent rounded-full blur-xl"></div>
        
        <div className="relative z-10 h-full flex flex-col justify-between">
          <div className="flex items-start justify-between">
            <div className="max-w-2xl">
              <div className="inline-flex items-center px-2 py-0.5 bg-white/70 backdrop-blur-sm rounded-full border border-purple-200/50 mb-2">
                <div className="w-1 h-1 bg-green-400 rounded-full mr-1.5 animate-pulse"></div>
                <span className="text-xs font-medium text-purple-800">Analytics Dashboard</span>
              </div>
              <h1 className="text-xl font-bold mb-1 bg-gradient-to-r from-purple-800 via-pink-700 to-blue-800 bg-clip-text text-transparent leading-tight">
                Advanced Report Center
              </h1>
              <p className="text-xs text-slate-700 leading-relaxed">
                Transform your institutional data into powerful insights with our comprehensive analytics platform.
              </p>
            </div>
            <div className="flex flex-col gap-2 ml-4">
              <button className="bg-gradient-to-r from-purple-300 to-pink-300 hover:from-purple-400 hover:to-pink-400 text-purple-900 px-4 py-2 rounded-md text-sm font-semibold transition-all duration-300 flex items-center gap-2 shadow-md shadow-purple-200/60 hover:shadow-lg hover:shadow-purple-300/70 hover:-translate-y-0.5 border border-purple-200/50">
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                </svg>
                Create Report
              </button>
              <button className="bg-gradient-to-r from-blue-300 to-cyan-300 hover:from-blue-400 hover:to-cyan-400 text-blue-900 px-4 py-2 rounded-md text-sm font-semibold transition-all duration-300 flex items-center gap-2 shadow-md shadow-blue-200/60 hover:shadow-lg hover:shadow-blue-300/70 hover:-translate-y-0.5 border border-blue-200/50">
                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export Data
              </button>
            </div>
          </div>

          {/* Enhanced Stats Overlay - Direct on Background */}
          <div className="grid grid-cols-4 gap-10">
            {/* Total Reports */}
            <div className="flex items-center gap-4 opacity-90 hover:opacity-100 transition-all duration-300 cursor-pointer group">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border border-white/30">
                <svg className="w-8 h-8 text-purple-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold text-purple-800 leading-tight drop-shadow-sm">25</p>
                <p className="text-sm font-medium text-purple-700 leading-normal drop-shadow-sm">Total Reports</p>
              </div>
            </div>

            {/* Active Dashboard */}
            <div className="flex items-center gap-4 opacity-90 hover:opacity-100 transition-all duration-300 cursor-pointer group">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border border-white/30">
                <svg className="w-8 h-8 text-pink-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold text-pink-800 leading-tight drop-shadow-sm">9</p>
                <p className="text-sm font-medium text-pink-700 leading-normal drop-shadow-sm">Active Dashboard</p>
              </div>
            </div>

            {/* Pending Reports */}
            <div className="flex items-center gap-4 opacity-90 hover:opacity-100 transition-all duration-300 cursor-pointer group">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border border-white/30">
                <svg className="w-8 h-8 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold text-blue-800 leading-tight drop-shadow-sm">3</p>
                <p className="text-sm font-medium text-blue-700 leading-normal drop-shadow-sm">Pending Scheduled</p>
              </div>
            </div>

            {/* Monthly Downloads */}
            <div className="flex items-center gap-4 opacity-90 hover:opacity-100 transition-all duration-300 cursor-pointer group">
              <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border border-white/30">
                <svg className="w-8 h-8 text-green-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <p className="text-2xl font-bold text-green-800 leading-tight drop-shadow-sm">1.5K+</p>
                <p className="text-sm font-medium text-green-700 leading-normal drop-shadow-sm">Monthly Downloads</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Combined Navigation and Dashboard Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-900">
          {activeTab === 'dashboard' ? 'My Dashboard Reports' : 'All Available Reports'}
        </h2>
        
        <div className="flex items-center gap-4">
          {/* Filter/Generate buttons for Available Reports */}
          {activeTab === 'available' && (
            <div className="flex gap-2">
              <button className="bg-white border border-blue-300 text-blue-600 px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-50 transition-colors">
                Filter Reports
              </button>
              <button className="bg-blue-300 hover:bg-blue-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                Generate Report
              </button>
            </div>
          )}
          
          {/* Navigation Tabs - Right Side */}
          <div className="bg-white rounded-lg border border-slate-200 shadow-sm overflow-hidden w-fit">
            <div className="flex">
              {[
                { key: 'dashboard', label: 'Dashboard', count: 9 },
                { key: 'available', label: 'Templates', count: 25 }
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`px-2.5 py-1.5 text-xs font-medium transition-all duration-200 border-b-2 ${
                    activeTab === tab.key
                      ? 'border-blue-500 text-blue-700 bg-blue-50/50'
                      : 'border-transparent text-slate-600 hover:text-blue-600 hover:bg-slate-50'
                  }`}
                >
                  <span className="flex items-center gap-1">
                    {tab.label}
                    <span className={`px-1 py-0.5 rounded-full text-xs font-medium ${
                      activeTab === tab.key
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-slate-100 text-slate-500'
                    }`}>
                      {tab.count}
                    </span>
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'dashboard' && (
        <>
          {/* My Dashboard Reports Grid */}
          <div className="mb-6">
            <div className="grid grid-cols-5 gap-4 max-w-full">
              {[
                {
                  title: 'Class 10A - Term 1 Report',
                  desc: 'Comprehensive academic performance for Class 10A, covering all subjects for the first term.',
                  generated: 'Generated: Oct 20, 2023',
                  lastViewed: 'Last viewed: 2 hours ago',
                  icon: (
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  ),
                },
                {
                  title: 'Monthly Attendance Summary',
                  desc: 'Student attendance summary for all classes for September. Highlights absentees and tardiness.',
                  generated: 'Generated: Oct 01, 2023',
                  lastViewed: 'Last viewed: 1 day ago',
                  icon: (
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ),
                },
                {
                  title: 'Fee Collection - August',
                  desc: 'Detailed breakdown of fee collection status, outstanding payments, and revenue analysis.',
                  generated: 'Generated: Sep 05, 2023',
                  lastViewed: 'Last viewed: 3 days ago',
                  icon: (
                    <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  ),
                },
                {
                  title: 'Staff Performance Review',
                  desc: 'Quarterly evaluation of teaching staff performance metrics and student feedback analysis.',
                  generated: 'Generated: Oct 15, 2023',
                  lastViewed: 'Last viewed: 4 hours ago',
                  icon: (
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  ),
                },
              ].map((report, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-2xl transition-all duration-300 cursor-pointer group shadow-xl shadow-gray-400/30 hover:shadow-purple-400/40 hover:-translate-y-2 relative">
                  <div className="flex items-start justify-between mb-3">
                    <div className="p-2.5 rounded-xl bg-gradient-to-br from-gray-50 to-gray-100 shadow-inner">
                      {report.icon}
                    </div>
                    <div className="flex gap-1.5">
                      <button className="flex items-center gap-1 px-2 py-1 bg-white border border-gray-300 rounded-lg text-xs font-medium text-gray-700 hover:bg-gray-50 transition-colors shadow-sm">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View
                      </button>
                      <button className="flex items-center gap-1 px-2 py-1 bg-gradient-to-r from-blue-400 to-purple-400 hover:from-blue-500 hover:to-purple-500 text-white rounded-lg text-xs font-medium transition-all duration-300 shadow-lg">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Download
                      </button>
                    </div>
                  </div>
                  <h3 className="font-bold text-gray-900 mb-2 text-sm leading-tight">{report.title}</h3>
                  <p className="text-xs text-gray-600 mb-3 line-clamp-2 leading-relaxed">{report.desc}</p>
                  <div className="space-y-1 mb-4">
                    <p className="text-xs text-gray-500">{report.generated}</p>
                    <p className="text-xs text-blue-600 font-medium">{report.lastViewed}</p>
                  </div>
                </div>
              ))}

              {/* Add New Report Card */}
              <div className="border-2 border-dashed border-purple-300 rounded-xl p-3 flex flex-col items-center justify-center text-center hover:border-purple-400 transition-all duration-300 cursor-pointer group bg-gradient-to-br from-purple-50/50 to-pink-50/50 shadow-xl shadow-purple-200/40 hover:shadow-2xl hover:shadow-purple-300/50 hover:-translate-y-2">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-200 to-purple-300 rounded-full flex items-center justify-center mb-2 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                  <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                </div>
                <h3 className="font-bold text-purple-700 text-xs mb-1">Create New Report</h3>
                <p className="text-xs text-purple-600 leading-relaxed">Build a custom report for your dashboard</p>
              </div>
            </div>
          </div>
        </>
      )}

      {activeTab === 'available' && (
        <>
          {/* Report Categories and Templates */}
          <div className="mb-6">
            {/* Report Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {[
                {
                  title: 'Academic Reports',
                  count: '12 templates',
                  desc: 'Student performance, grades, and academic analytics',
                  icon: (
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                  ),
                  bgColor: 'bg-blue-50',
                  borderColor: 'border-blue-200',
                  color: 'text-blue-600'
                },
                {
                  title: 'Attendance Reports',
                  count: '8 templates',
                  desc: 'Daily, weekly, and monthly attendance tracking',
                  icon: (
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ),
                  bgColor: 'bg-green-50',
                  borderColor: 'border-green-200',
                  color: 'text-green-600'
                },
                {
                  title: 'Financial Reports',
                  count: '6 templates',
                  desc: 'Fee collection, expenses, and financial analytics',
                  icon: (
                    <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  ),
                  bgColor: 'bg-yellow-50',
                  borderColor: 'border-yellow-200',
                  color: 'text-yellow-600'
                },
                {
                  title: 'Administrative',
                  count: '10 templates',
                  desc: 'Staff, schedules, and operational reports',
                  icon: (
                    <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  ),
                  bgColor: 'bg-purple-50',
                  borderColor: 'border-purple-200',
                  color: 'text-purple-600'
                },
              ].map((category, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer group shadow-blue-100/30">
                  <div className="flex items-center justify-between mb-3">
                    <div className="p-2 rounded-lg bg-gray-50">
                      {category.icon}
                    </div>
                    <span className={`text-xs font-medium ${category.color} bg-gray-50 px-2 py-1 rounded-full`}>
                      {category.count}
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-1 text-sm">{category.title}</h3>
                  <p className="text-xs text-gray-600">{category.desc}</p>
                </div>
              ))}
            </div>

            {/* Available Report Templates */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                {
                  title: 'Class Performance Analysis',
                  desc: 'Comprehensive academic performance analysis by class and subject',
                  category: 'Academic',
                  estimatedTime: '5-10 minutes',
                  popularity: 'Popular',
                  icon: (
                    <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  ),
                  bgColor: 'bg-blue-50',
                  borderColor: 'border-blue-200'
                },
                {
                  title: 'Monthly Attendance Report',
                  desc: 'Detailed attendance tracking with absentee analysis',
                  category: 'Attendance',
                  estimatedTime: '2-5 minutes',
                  popularity: 'Most Used',
                  icon: (
                    <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  ),
                  bgColor: 'bg-green-50',
                  borderColor: 'border-green-200'
                },
                {
                  title: 'Fee Collection Summary',
                  desc: 'Financial overview with payment status and outstanding dues',
                  category: 'Financial',
                  estimatedTime: '3-7 minutes',
                  popularity: 'Trending',
                  icon: (
                    <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  ),
                  bgColor: 'bg-yellow-50',
                  borderColor: 'border-yellow-200'
                },
                {
                  title: 'Teacher Performance Review',
                  desc: 'Staff evaluation and performance metrics analysis',
                  category: 'Administrative',
                  estimatedTime: '10-15 minutes',
                  popularity: 'New',
                  icon: (
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  ),
                  bgColor: 'bg-purple-50',
                  borderColor: 'border-purple-200'
                },
                {
                  title: 'Student Progress Tracker',
                  desc: 'Individual student progress tracking with trends',
                  category: 'Academic',
                  estimatedTime: '7-12 minutes',
                  popularity: 'Popular',
                  icon: (
                    <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  ),
                  bgColor: 'bg-indigo-50',
                  borderColor: 'border-indigo-200'
                },
                {
                  title: 'Exam Results Analysis',
                  desc: 'Comprehensive examination results with statistical insights',
                  category: 'Academic',
                  estimatedTime: '8-15 minutes',
                  popularity: 'Popular',
                  icon: (
                    <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v6a2 2 0 002 2h2m0 0h2m0 0h2a2 2 0 002-2V7a2 2 0 00-2-2h-2m0 0V5a2 2 0 00-2-2M9 5a2 2 0 012 2v2a2 2 0 01-2 2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                  ),
                  bgColor: 'bg-red-50',
                  borderColor: 'border-red-200'
                },
              ].map((report, index) => (
                <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-all duration-200 cursor-pointer group shadow-blue-100/50">
                  <div className="flex items-start justify-between mb-3">
                    <div className="p-2 rounded-lg bg-gray-50">
                      {report.icon}
                    </div>
                    <div className="flex flex-col items-end gap-1">
                      <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                        report.popularity === 'Most Used' ? 'bg-green-100 text-green-700' :
                        report.popularity === 'Popular' ? 'bg-blue-100 text-blue-700' :
                        report.popularity === 'Trending' ? 'bg-orange-100 text-orange-700' :
                        'bg-gray-100 text-gray-700'
                      }`}>
                        {report.popularity}
                      </span>
                    </div>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2 text-sm">{report.title}</h3>
                  <p className="text-xs text-gray-600 mb-3 line-clamp-2">{report.desc}</p>
                  <div className="space-y-1 mb-3">
                    <p className="text-xs text-gray-500">Category: {report.category}</p>
                    <p className="text-xs text-blue-600">Est. time: {report.estimatedTime}</p>
                  </div>
                  <div className="flex gap-2">
                    <button className="flex items-center gap-1 px-3 py-1.5 bg-white border border-gray-300 rounded text-xs font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      Preview
                    </button>
                    <button className="flex items-center gap-1 px-3 py-1.5 bg-blue-300 hover:bg-blue-400 text-white rounded text-xs font-medium transition-colors">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                      </svg>
                      Generate
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {/* Report Usage & Trends Section - Shared across tabs */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Report Usage & Trends</h2>
          <button className="text-blue-400 hover:text-blue-600 font-medium flex items-center gap-1 text-sm">
            View Detailed Analytics
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Usage Chart Placeholder */}
          <div className="lg:col-span-2 bg-white rounded-lg border border-blue-200 p-4">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold text-gray-900 text-sm">Monthly Report Generation</h3>
              <div className="flex gap-1">
                <button className="px-2 py-1 text-xs bg-blue-300 text-white rounded-lg">This Year</button>
                <button className="px-2 py-1 text-xs text-blue-600 hover:bg-blue-50 rounded-lg">Last Year</button>
              </div>
            </div>
            <div className="h-48 bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-100 relative overflow-hidden">
              {/* Chart Background Grid */}
              <div className="absolute inset-4 opacity-30">
                {/* Horizontal Grid Lines */}
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="absolute w-full border-t border-blue-400" style={{ top: `${i * 20}%` }}></div>
                ))}
                {/* Vertical Grid Lines */}
                {[...Array(7)].map((_, i) => (
                  <div key={i} className="absolute h-full border-l border-blue-400" style={{ left: `${(i + 1) * 14.28}%` }}></div>
                ))}
              </div>
              
              {/* Chart Content */}
              <div className="relative z-10 h-full">
                {/* Y-axis Labels */}
                <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-blue-700 font-semibold py-2">
                  <span>200</span>
                  <span>160</span>
                  <span>120</span>
                  <span>80</span>
                  <span>40</span>
                  <span>0</span>
                </div>
                
                {/* Chart Area */}
                <div className="ml-8 mr-4 h-full flex flex-col">
                  {/* Line Chart Container */}
                  <div className="flex-1 relative">
                    {/* Data Points */}
                    <div className="absolute inset-0">
                      {/* SVG for the line chart */}
                      <svg className="w-full h-full" viewBox="0 0 400 160" preserveAspectRatio="none">
                        {/* Line path */}
                        <path
                          d="M 30 88 L 90 64 L 150 40 L 210 27 L 270 44 L 330 18"
                          stroke="url(#lineGradient)"
                          strokeWidth="3"
                          fill="none"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="drop-shadow-sm"
                        />
                        {/* Area under the line */}
                        <path
                          d="M 30 88 L 90 64 L 150 40 L 210 27 L 270 44 L 330 18 L 330 160 L 30 160 Z"
                          fill="url(#areaGradient)"
                          opacity="0.3"
                        />
                        {/* Gradient definitions */}
                        <defs>
                          <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" stopColor="#3b82f6" />
                            <stop offset="25%" stopColor="#8b5cf6" />
                            <stop offset="50%" stopColor="#10b981" />
                            <stop offset="75%" stopColor="#f59e0b" />
                            <stop offset="100%" stopColor="#ec4899" />
                          </linearGradient>
                          <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                            <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.4" />
                            <stop offset="100%" stopColor="#3b82f6" stopOpacity="0.1" />
                          </linearGradient>
                        </defs>
                      </svg>
                      
                      {/* Data point circles */}
                      <div className="absolute" style={{ left: '7.5%', top: '55%' }}>
                        <div className="w-3 h-3 bg-blue-600 rounded-full border-2 border-white shadow-lg"></div>
                      </div>
                      <div className="absolute" style={{ left: '22.5%', top: '40%' }}>
                        <div className="w-3 h-3 bg-purple-600 rounded-full border-2 border-white shadow-lg"></div>
                      </div>
                      <div className="absolute" style={{ left: '37.5%', top: '25%' }}>
                        <div className="w-3 h-3 bg-green-600 rounded-full border-2 border-white shadow-lg"></div>
                      </div>
                      <div className="absolute" style={{ left: '52.5%', top: '17%' }}>
                        <div className="w-3 h-3 bg-yellow-600 rounded-full border-2 border-white shadow-lg"></div>
                      </div>
                      <div className="absolute" style={{ left: '67.5%', top: '27%' }}>
                        <div className="w-3 h-3 bg-pink-600 rounded-full border-2 border-white shadow-lg"></div>
                      </div>
                      <div className="absolute" style={{ left: '82.5%', top: '11%' }}>
                        <div className="w-3 h-3 bg-indigo-600 rounded-full border-2 border-white shadow-lg"></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* X-axis Labels */}
                  <div className="flex justify-between items-center mt-2 px-2">
                    <div className="flex flex-col items-center">
                      <span className="text-xs text-blue-800 font-bold">Jan</span>
                      <span className="text-[10px] text-blue-600">90</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="text-xs text-blue-800 font-bold">Feb</span>
                      <span className="text-[10px] text-blue-600">120</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="text-xs text-blue-800 font-bold">Mar</span>
                      <span className="text-[10px] text-blue-600">150</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="text-xs text-blue-800 font-bold">Apr</span>
                      <span className="text-[10px] text-blue-600">170</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="text-xs text-blue-800 font-bold">May</span>
                      <span className="text-[10px] text-blue-600">140</span>
                    </div>
                    <div className="flex flex-col items-center">
                      <span className="text-xs text-blue-800 font-bold">Jun</span>
                      <span className="text-[10px] text-blue-600">180</span>
                    </div>
                  </div>
                  
                  {/* X-axis line */}
                  <div className="w-full h-0.5 bg-blue-400 mt-1"></div>
                </div>
                
                {/* Chart Info */}
                <div className="absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1.5 text-xs text-blue-800 font-bold shadow-md border border-blue-200">
                  Total: 1,247 downloads
                </div>
                
                {/* Chart Legend */}
                <div className="absolute bottom-2 left-8 flex items-center gap-3 text-xs">
                  <div className="flex items-center gap-1">
                    <div className="w-3 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600"></div>
                    <span className="text-blue-700 font-medium">Downloads Trend</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Top Reports */}
          <div className="bg-white rounded-lg border border-blue-200 p-4">
            <h3 className="font-semibold text-gray-900 mb-3 text-sm">Most Popular Reports</h3>
            <div className="space-y-2">
              {[
                { name: 'Student Performance', count: '156 downloads', trend: '+12%' },
                { name: 'Attendance Summary', count: '134 downloads', trend: '+8%' },
                { name: 'Fee Collection', count: '98 downloads', trend: '+15%' },
                { name: 'Class Schedule', count: '87 downloads', trend: '+5%' },
              ].map((report, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900">{report.name}</p>
                    <p className="text-xs text-gray-500">{report.count}</p>
                  </div>
                  <span className="text-xs font-medium text-green-600">{report.trend}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
