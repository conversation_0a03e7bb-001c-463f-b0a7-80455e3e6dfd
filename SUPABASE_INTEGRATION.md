# Supabase Integration Guide

This guide provides comprehensive documentation for understanding and working with <PERSON>pa<PERSON> in the EduPro project. It covers setup, configuration, database design, and best practices for new team members.

## 📋 Table of Contents

1. [Overview](#overview)
2. [Supabase Setup](#supabase-setup)
3. [Database Schema](#database-schema)
4. [Environment Configuration](#environment-configuration)
5. [Client Configuration](#client-configuration)
6. [Authentication](#authentication)
7. [Database Operations](#database-operations)
8. [File Storage](#file-storage)
9. [Row Level Security (RLS)](#row-level-security-rls)
10. [Real-time Features](#real-time-features)
11. [Best Practices](#best-practices)
12. [Troubleshooting](#troubleshooting)

## Overview

Supabase serves as our Backend-as-a-Service (BaaS) solution, providing:

- **PostgreSQL Database** - Primary data storage
- **Authentication** - User management and security
- **Storage** - File upload and management
- **Real-time** - Live data synchronization
- **Edge Functions** - Serverless compute (if needed)
- **Row Level Security** - Database-level access control

## Supabase Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Sign up/login to your account
3. Click "New Project"
4. Choose your organization
5. Fill project details:
   - **Name**: `edupro-[environment]` (e.g., `edupro-dev`, `edupro-prod`)
   - **Database Password**: Generate a strong password
   - **Region**: Choose closest to your users
6. Click "Create new project"

### 2. Get Project Credentials

After project creation, navigate to **Settings > API** to find:

```
Project URL: https://your-project-id.supabase.co
Anon/Public Key: eyJ...
Service Role Key: eyJ... (Keep this secret!)
```

### 3. Configure Database

Navigate to **SQL Editor** in Supabase dashboard and run the database setup scripts.

## Database Schema

### Core Tables Overview

Our database follows a normalized design with clear relationships:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    students     │    │   guardians     │    │academic_records │
│                 │    │                 │    │                 │
│ • id (PK)       │    │ • id (PK)       │    │ • id (PK)       │
│ • first_name    │    │ • student_id(FK)│    │ • student_id(FK)│
│ • last_name     │    │ • name          │    │ • class_id (FK) │
│ • date_of_birth │    │ • relation_id   │    │ • section_id(FK)│
│ • gender        │    │ • phone         │    │ • roll_number   │
│ • email         │    │ • email         │    │ • academic_year │
│ • phone_number  │    │ • is_primary    │    └─────────────────┘
│ • address       │    └─────────────────┘              │
│ • blood_group   │              │                      │
│ • nationality   │              │                      │
│ • religion      │              │                      │
│ • is_active     │              │                      │
│ • created_at    │              │                      │
│ • updated_at    │              │                      │
└─────────────────┘              │                      │
         │                       │                      │
         └───────────────────────┼──────────────────────┘
                                 │
┌─────────────────┐             │    ┌─────────────────┐
│   documents     │             │    │ master tables   │
│                 │             │    │                 │
│ • id (PK)       │             │    │ • classes       │
│ • student_id(FK)│─────────────┘    │ • sections      │
│ • type          │                  │ • academic_years│
│ • file_name     │                  │ • guardian_rels │
│ • file_path     │                  └─────────────────┘
│ • file_url      │
│ • file_size     │
│ • mime_type     │
│ • status        │
│ • is_required   │
│ • uploaded_at   │
└─────────────────┘
```

### Database Setup Script

Run this in Supabase SQL Editor:

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE user_role AS ENUM ('student', 'teacher', 'admin', 'parent');
CREATE TYPE gender_type AS ENUM ('male', 'female', 'other');
CREATE TYPE document_status AS ENUM ('pending', 'approved', 'rejected');

-- 1. Profiles table (extends Supabase auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    role user_role DEFAULT 'student',
    avatar_url TEXT,
    phone TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2. Master Data Tables
CREATE TABLE public.classes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    grade_level INTEGER,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public.sections (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    capacity INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public.academic_years (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    year TEXT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    is_current BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public.guardian_relations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 3. Core Tables
CREATE TABLE public.students (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    date_of_birth DATE NOT NULL,
    gender gender_type NOT NULL,
    email TEXT,
    phone_number TEXT,
    address TEXT,
    blood_group TEXT,
    nationality TEXT DEFAULT 'Indian',
    religion TEXT,
    medical_conditions TEXT,
    profile_id UUID REFERENCES public.profiles(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public.guardians (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES public.students(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    relation_id UUID REFERENCES public.guardian_relations(id) NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    address TEXT,
    occupation TEXT,
    emergency_contact TEXT,
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE public.academic_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES public.students(id) ON DELETE CASCADE NOT NULL,
    class_id UUID REFERENCES public.classes(id) NOT NULL,
    section_id UUID REFERENCES public.sections(id) NOT NULL,
    academic_year_id UUID REFERENCES public.academic_years(id) NOT NULL,
    roll_number TEXT NOT NULL,
    admission_date DATE NOT NULL,
    previous_school TEXT,
    admission_number TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(roll_number, class_id, section_id, academic_year_id)
);

CREATE TABLE public.documents (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    student_id UUID REFERENCES public.students(id) ON DELETE CASCADE NOT NULL,
    type TEXT NOT NULL,
    file_name TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_url TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type TEXT NOT NULL,
    status document_status DEFAULT 'pending',
    is_required BOOLEAN DEFAULT false,
    notes TEXT,
    uploaded_at TIMESTAMPTZ DEFAULT NOW(),
    reviewed_at TIMESTAMPTZ,
    reviewed_by UUID REFERENCES public.profiles(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 4. System Tables
CREATE TABLE public.enrollment_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    current_step TEXT NOT NULL,
    status TEXT DEFAULT 'draft',
    data JSONB DEFAULT '{}',
    validation_errors JSONB DEFAULT '{}',
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '24 hours'),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 5. Indexes for Performance
CREATE INDEX idx_students_name ON public.students(first_name, last_name);
CREATE INDEX idx_students_email ON public.students(email) WHERE email IS NOT NULL;
CREATE INDEX idx_guardians_student_id ON public.guardians(student_id);
CREATE INDEX idx_guardians_phone ON public.guardians(phone);
CREATE INDEX idx_academic_records_student ON public.academic_records(student_id);
CREATE INDEX idx_academic_records_class_section ON public.academic_records(class_id, section_id);
CREATE INDEX idx_academic_records_roll_number ON public.academic_records(roll_number);
CREATE INDEX idx_documents_student_id ON public.documents(student_id);
CREATE INDEX idx_documents_type ON public.documents(type);

-- 6. Functions and Triggers for updated_at
CREATE OR REPLACE FUNCTION public.handle_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to all tables
CREATE TRIGGER handle_updated_at_profiles BEFORE UPDATE ON public.profiles FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_students BEFORE UPDATE ON public.students FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_guardians BEFORE UPDATE ON public.guardians FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_academic_records BEFORE UPDATE ON public.academic_records FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_documents BEFORE UPDATE ON public.documents FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_classes BEFORE UPDATE ON public.classes FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_sections BEFORE UPDATE ON public.sections FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_academic_years BEFORE UPDATE ON public.academic_years FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_guardian_relations BEFORE UPDATE ON public.guardian_relations FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();
CREATE TRIGGER handle_updated_at_enrollment_sessions BEFORE UPDATE ON public.enrollment_sessions FOR EACH ROW EXECUTE PROCEDURE public.handle_updated_at();

-- 7. Insert Sample Master Data
INSERT INTO public.guardian_relations (name, description) VALUES
('Father', 'Biological or adoptive father'),
('Mother', 'Biological or adoptive mother'),
('Guardian', 'Legal guardian'),
('Grandfather', 'Paternal or maternal grandfather'),
('Grandmother', 'Paternal or maternal grandmother'),
('Uncle', 'Uncle or aunt'),
('Other', 'Other family relation');

INSERT INTO public.classes (name, description, grade_level) VALUES
('Nursery', 'Pre-school nursery class', 0),
('LKG', 'Lower Kindergarten', 1),
('UKG', 'Upper Kindergarten', 2),
('Class 1', 'First standard', 3),
('Class 2', 'Second standard', 4),
('Class 3', 'Third standard', 5),
('Class 4', 'Fourth standard', 6),
('Class 5', 'Fifth standard', 7),
('Class 6', 'Sixth standard', 8),
('Class 7', 'Seventh standard', 9),
('Class 8', 'Eighth standard', 10),
('Class 9', 'Ninth standard', 11),
('Class 10', 'Tenth standard', 12);

INSERT INTO public.sections (name, description, capacity) VALUES
('A', 'Section A', 30),
('B', 'Section B', 30),
('C', 'Section C', 30),
('D', 'Section D', 30);

INSERT INTO public.academic_years (year, start_date, end_date, is_current) VALUES
('2024-25', '2024-06-01', '2025-03-31', true),
('2025-26', '2025-06-01', '2026-03-31', false);
```

## Environment Configuration

### Development Environment (.env.local)

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Optional Configuration
NEXT_PUBLIC_APP_NAME=EduPro
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENVIRONMENT=development

# Storage Configuration
NEXT_PUBLIC_SUPABASE_STORAGE_BUCKET=documents
```

### Production Environment

Use the same variables but with production Supabase project credentials.

**⚠️ Security Note**: Never commit `.env` files to version control!

## Client Configuration

### Supabase Client Setup

**File: `src/lib/supabase.ts`**

```typescript
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

// Server-side client (for API routes)
export const createServerSupabaseClient = () => {
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
  
  if (!serviceRoleKey) {
    throw new Error('Missing Supabase service role key');
  }

  return createClient<Database>(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

export type SupabaseClient = typeof supabase;
```

### Type Generation

Generate TypeScript types from your database:

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Generate types
supabase gen types typescript --project-id your-project-id > src/types/database.ts
```

## Authentication

### Auth Provider Setup

**File: `src/components/auth/auth-provider.tsx`**

```typescript
'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);
    };

    getSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    if (error) throw error;
  };

  const signUp = async (email: string, password: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password
    });
    if (error) throw error;
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    if (error) throw error;
  };

  const value = {
    user,
    session,
    loading,
    signIn,
    signUp,
    signOut,
    resetPassword
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

## Database Operations

### Repository Pattern Implementation

**File: `src/repositories/baseRepository.ts`**

```typescript
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

export interface PaginationOptions {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResult<T> {
  data: T[];
  count: number;
  page: number;
  limit: number;
  totalPages: number;
}

export abstract class BaseRepository<T, TInsert, TUpdate> {
  protected client: SupabaseClient<Database>;
  protected tableName: string;

  constructor(client: SupabaseClient<Database>, tableName: string) {
    this.client = client;
    this.tableName = tableName;
  }

  async findById(id: string): Promise<T | null> {
    const { data, error } = await this.client
      .from(this.tableName)
      .select('*')
      .eq('id', id)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return data as T;
  }

  async findAll(
    filters: Record<string, any> = {},
    options: PaginationOptions = {}
  ): Promise<PaginatedResult<T>> {
    const { page = 1, limit = 50, sortBy = 'created_at', sortOrder = 'desc' } = options;
    
    let query = this.client
      .from(this.tableName)
      .select('*', { count: 'exact' })
      .eq('is_active', true);

    // Apply filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query = query.eq(key, value);
      }
    });

    // Apply sorting and pagination
    const { data, error, count } = await query
      .order(sortBy, { ascending: sortOrder === 'asc' })
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw error;

    return {
      data: (data || []) as T[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }

  async create(data: TInsert): Promise<T> {
    const { data: result, error } = await this.client
      .from(this.tableName)
      .insert([data])
      .select()
      .single();

    if (error) throw error;
    return result as T;
  }

  async update(id: string, data: TUpdate): Promise<T> {
    const { data: result, error } = await this.client
      .from(this.tableName)
      .update(data)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return result as T;
  }

  async softDelete(id: string): Promise<void> {
    const { error } = await this.client
      .from(this.tableName)
      .update({ is_active: false, updated_at: new Date().toISOString() })
      .eq('id', id);

    if (error) throw error;
  }

  async hardDelete(id: string): Promise<void> {
    const { error } = await this.client
      .from(this.tableName)
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    console[level](`[${this.constructor.name}] ${message}`, data);
  }
}
```

### Specific Repository Example

**File: `src/repositories/studentRepository.ts`**

```typescript
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';
import { BaseRepository, PaginatedResult, PaginationOptions } from './baseRepository';
import { 
  StudentEntity, 
  StudentInsert, 
  StudentUpdate,
  DATABASE_TABLES,
  DATABASE_COLUMNS
} from '@/constants/database';

export class StudentRepository extends BaseRepository<StudentEntity, StudentInsert, StudentUpdate> {
  constructor(client: SupabaseClient<Database>) {
    super(client, DATABASE_TABLES.STUDENTS);
  }

  async findByEmail(email: string): Promise<StudentEntity | null> {
    const { data, error } = await this.client
      .from(this.tableName)
      .select('*')
      .eq(DATABASE_COLUMNS.STUDENTS.EMAIL, email)
      .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return data as StudentEntity;
  }

  async searchStudents(
    searchTerm: string, 
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<StudentEntity>> {
    const { page = 1, limit = 50 } = pagination || {};
    
    const { data, error, count } = await this.client
      .from(this.tableName)
      .select('*', { count: 'exact' })
      .or(`first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`)
      .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
      .order(DATABASE_COLUMNS.STUDENTS.FIRST_NAME)
      .range((page - 1) * limit, page * limit - 1);

    if (error) throw error;

    return {
      data: (data || []) as StudentEntity[],
      count: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  }
}
```

## File Storage

### Storage Bucket Setup

1. Go to **Storage** in Supabase dashboard
2. Create a new bucket called `documents`
3. Set appropriate permissions

### Storage Service Implementation

**File: `src/services/document/storageService.ts`**

```typescript
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

export interface UploadOptions {
  bucket?: string;
  folder?: string;
  fileName?: string;
  metadata?: Record<string, any>;
}

export interface UploadResult {
  success: boolean;
  path?: string;
  url?: string;
  error?: string;
}

export class StorageService {
  private client: SupabaseClient<Database>;
  private defaultBucket = 'documents';

  constructor(client: SupabaseClient<Database>) {
    this.client = client;
  }

  async uploadFile(
    file: File,
    filePath: string,
    onProgress?: (progress: number) => void
  ): Promise<UploadResult> {
    try {
      const { data, error } = await this.client.storage
        .from(this.defaultBucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        return { success: false, error: error.message };
      }

      const { data: urlData } = this.client.storage
        .from(this.defaultBucket)
        .getPublicUrl(data.path);

      return {
        success: true,
        path: data.path,
        url: urlData.publicUrl
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  async deleteFile(filePath: string): Promise<boolean> {
    try {
      const { error } = await this.client.storage
        .from(this.defaultBucket)
        .remove([filePath]);

      return !error;
    } catch (error) {
      console.error('Storage delete error:', error);
      return false;
    }
  }

  async getFileUrl(filePath: string, expiresIn: number = 3600): Promise<string> {
    const { data } = await this.client.storage
      .from(this.defaultBucket)
      .createSignedUrl(filePath, expiresIn);

    return data?.signedUrl || '';
  }
}
```

## Row Level Security (RLS)

### Enable RLS and Create Policies

Run in Supabase SQL Editor:

```sql
-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.students ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.guardians ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.academic_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- Students policies (Admin access for now)
CREATE POLICY "Admin can manage students" ON public.students
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Similar policies for other tables...
-- Add more specific policies based on your requirements

-- Storage policies
INSERT INTO storage.buckets (id, name, public) VALUES ('documents', 'documents', false);

CREATE POLICY "Admin can upload documents" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'documents' AND
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Admin can view documents" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'documents' AND
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
```

## Real-time Features

### Setting Up Real-time Subscriptions

```typescript
import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';

export function useRealtimeStudents() {
  const [students, setStudents] = useState([]);

  useEffect(() => {
    // Subscribe to INSERT operations
    const subscription = supabase
      .channel('students-changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'students'
        },
        (payload) => {
          console.log('New student added:', payload.new);
          setStudents(prev => [...prev, payload.new]);
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'students'
        },
        (payload) => {
          console.log('Student updated:', payload.new);
          setStudents(prev => 
            prev.map(student => 
              student.id === payload.new.id ? payload.new : student
            )
          );
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(subscription);
    };
  }, []);

  return students;
}
```

## Best Practices

### 1. Database Operations

✅ **DO:**
- Use transactions for related operations
- Implement proper error handling
- Use typed queries with generated types
- Follow the repository pattern
- Use soft deletes where appropriate

❌ **DON'T:**
- Hardcode table/column names
- Ignore error handling
- Use `any` types
- Make direct database calls from components

### 2. Authentication

✅ **DO:**
- Use RLS policies for security
- Implement proper session management
- Handle auth state changes
- Validate user permissions

❌ **DON'T:**
- Trust client-side authentication only
- Store sensitive data in localStorage
- Ignore session expiration

### 3. Performance

✅ **DO:**
- Use indexes on frequently queried columns
- Implement pagination for large datasets
- Use `select()` to limit returned columns
- Cache static data (master tables)

❌ **DON'T:**
- Load unnecessary data
- Make excessive database calls
- Ignore query performance

### 4. Error Handling

```typescript
// Good error handling example
async function createStudent(data: StudentInsert): Promise<StudentEntity> {
  try {
    const result = await studentRepository.create(data);
    return result;
  } catch (error) {
    if (error.code === '23505') { // Unique constraint violation
      throw new Error('Student with this email already exists');
    }
    if (error.code === '23503') { // Foreign key violation
      throw new Error('Invalid reference data provided');
    }
    throw new Error('Failed to create student');
  }
}
```

## Troubleshooting

### Common Issues

#### 1. Connection Issues

**Problem**: `Failed to fetch` or connection timeouts

**Solutions**:
- Check environment variables
- Verify Supabase project is running
- Check network connectivity
- Verify API keys are correct

#### 2. Authentication Issues

**Problem**: `Invalid JWT` or auth errors

**Solutions**:
- Check if user is authenticated
- Verify RLS policies
- Check token expiration
- Clear browser storage

#### 3. Database Query Issues

**Problem**: `Permission denied` or `Row not found`

**Solutions**:
- Check RLS policies
- Verify user permissions
- Check if data exists
- Review query filters

#### 4. Type Issues

**Problem**: TypeScript type errors

**Solutions**:
- Regenerate database types
- Check schema changes
- Verify import paths
- Update type definitions

### Debug Tools

#### 1. Connection Test

```typescript
// test-supabase-connection.js
const { createClient } = require('@supabase/supabase-js');

async function testConnection() {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );

  try {
    const { data, error } = await supabase.from('students').select('count');
    if (error) throw error;
    console.log('✅ Connection successful');
  } catch (error) {
    console.error('❌ Connection failed:', error);
  }
}

testConnection();
```

#### 2. Auth State Debug

```typescript
// Debug auth state
useEffect(() => {
  supabase.auth.onAuthStateChange((event, session) => {
    console.log('Auth event:', event);
    console.log('Session:', session);
  });
}, []);
```

#### 3. Query Debugging

```typescript
// Enable query logging in development
if (process.env.NODE_ENV === 'development') {
  supabase.from('students')
    .select('*')
    .then(result => {
      console.log('Query result:', result);
    });
}
```

### Getting Help

1. **Check Supabase Logs**: Go to Supabase Dashboard > Logs
2. **Review Database Schema**: Ensure tables match expected structure
3. **Test Queries**: Use Supabase SQL Editor to test queries
4. **Check Network**: Verify API endpoints are accessible
5. **Review Documentation**: Supabase docs are comprehensive

### Useful Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase TypeScript Support](https://supabase.com/docs/reference/javascript/typescript-support)
- [Row Level Security Guide](https://supabase.com/docs/learn/auth-deep-dive/auth-row-level-security)
- [Supabase CLI](https://supabase.com/docs/reference/cli)

---

This guide should help new team members understand and work with Supabase effectively. For project-specific questions, refer to the main README.md or reach out to the development team.
