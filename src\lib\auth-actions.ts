// src/lib/auth-actions.ts
'use server';

import { redirect } from 'next/navigation';
import { createSession, deleteSession } from './session';
import { signIn as supabaseSignIn, signOut as supabaseSignOut, signUp as supabaseSignUp } from './supabase-auth-functions';

export interface AuthResult {
  success: boolean;
  error?: string;
}

/**
 * Server action for user sign in
 */
export async function signInAction(email: string, password: string): Promise<AuthResult> {
  try {
    const result = await supabaseSignIn(email, password);
    
    if (result.success && result.user) {
      // Create secure session
      await createSession(result.user);
      return { success: true };
    }
    
    return { success: false, error: result.error || 'Sign in failed' };
  } catch (error) {
    console.error('Sign in action error:', error);
    return { success: false, error: 'Authentication service unavailable' };
  }
}

/**
 * Server action for user sign up
 */
export async function signUpAction(
  email: string, 
  password: string, 
  userData: { name: string; role: string }
): Promise<AuthResult> {
  try {
    const result = await supabaseSignUp(email, password, userData);
    
    if (result.success && result.user) {
      // Create secure session
      await createSession(result.user);
      return { success: true };
    }
    
    return { success: false, error: result.error || 'Sign up failed' };
  } catch (error) {
    console.error('Sign up action error:', error);
    return { success: false, error: 'Authentication service unavailable' };
  }
}

/**
 * Server action for user sign out
 */
export async function signOutAction(): Promise<void> {
  try {
    // Sign out from Supabase
    await supabaseSignOut();

    // Delete session
    await deleteSession();

    // Redirect to product page after logout
    redirect('/product');
  } catch (error) {
    console.error('Sign out action error:', error);
    // Force delete session and redirect even if Supabase signout fails
    await deleteSession();
    redirect('/product');
  }
}

/**
 * Server action to redirect to dashboard after successful authentication
 */
export async function redirectToDashboard(): Promise<void> {
  redirect('/dashboard');
}
