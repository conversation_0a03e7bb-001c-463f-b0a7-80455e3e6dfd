// src/app/api/master-data/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { MasterDataService } from '../../../services/masterDataService';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    let data;

    switch (type) {
      case 'classes':
        data = await MasterDataService.getClasses();
        break;
      case 'sections':
        data = await MasterDataService.getSections();
        break;
      case 'academic-years':
        data = await MasterDataService.getAcademicYears();
        break;
      case 'guardian-relations':
        data = await MasterDataService.getGuardianRelations();
        break;
      case 'current-academic-year':
        data = await MasterDataService.getCurrentAcademicYear();
        break;
      case 'all':
      default:
        data = await MasterDataService.getAllMasterData();
        break;
    }

    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Error in GET /api/master-data:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch master data'
      },
      { status: 500 }
    );
  }
}
