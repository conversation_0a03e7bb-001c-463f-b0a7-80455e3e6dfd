/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,

  // Use stable Webpack bundler instead of Turbopack
  // Turbopack can be enabled later when it's more stable

  // ESLint configuration
  eslint: {
    // Only run ESLint on specific directories during build
    dirs: ['src'],
    // Allow production builds to complete even if there are ESLint warnings
    ignoreDuringBuilds: true,
  },

  // TypeScript configuration
  typescript: {
    // Allow production builds to complete even if there are TypeScript errors
    ignoreBuildErrors: false,
  },
};

module.exports = nextConfig;