// src/app/fee-management/page.tsx
'use client';

import { useState } from 'react';

export default function FeeManagementPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl-app font-bold text-slate-900">Fee Management</h1>
          <p className="text-base-app text-slate-600 mt-1">
            Manage student fees, payments, and financial records
          </p>
        </div>
        <button className="btn-primary">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Record Payment
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-slate-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: '💰' },
            { id: 'payments', label: 'Payments', icon: '💳' },
            { id: 'dues', label: 'Outstanding Dues', icon: '⚠️' },
            { id: 'structure', label: 'Fee Structure', icon: '📋' },
            { id: 'reports', label: 'Financial Reports', icon: '📊' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm-app transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h2 className="text-xl-app font-semibold text-slate-900">Financial Overview</h2>
            
            {/* Monthly Summary */}
            <div className="bg-green-50 rounded-lg p-4 mb-6">
              <h3 className="text-lg-app font-medium text-green-900 mb-2">This Month's Collection</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl-app font-bold text-green-600">₹2,45,000</div>
                  <div className="text-sm-app text-green-700">Total Collected</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl-app font-bold text-blue-600">₹1,85,000</div>
                  <div className="text-sm-app text-blue-700">Pending Collection</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl-app font-bold text-orange-600">57%</div>
                  <div className="text-sm-app text-orange-700">Collection Rate</div>
                </div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Total Students', value: '450', change: '+15', color: 'blue' },
                { title: 'Paid This Month', value: '285', change: '+45', color: 'green' },
                { title: 'Pending Payments', value: '165', change: '-12', color: 'red' },
                { title: 'Overdue', value: '23', change: '+5', color: 'orange' },
              ].map((stat, index) => (
                <div key={index} className="bg-slate-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm-app font-medium text-slate-600">{stat.title}</p>
                      <p className="text-2xl-app font-bold text-slate-900">{stat.value}</p>
                    </div>
                    <div className={`text-sm-app font-medium ${
                      stat.change.startsWith('+') ? 'text-green-600' : 
                      stat.change.startsWith('-') ? 'text-red-600' : 'text-slate-500'
                    }`}>
                      {stat.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            <div>
              <h3 className="text-lg-app font-medium text-slate-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { title: 'Record Payment', icon: '💳', desc: 'Add new fee payment' },
                  { title: 'Send Reminders', icon: '📧', desc: 'Send payment reminders' },
                  { title: 'Generate Receipt', icon: '🧾', desc: 'Create payment receipt' },
                ].map((action, index) => (
                  <div key={index} className="border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                    <div className="text-2xl mb-2">{action.icon}</div>
                    <h4 className="text-base-app font-medium text-slate-900">{action.title}</h4>
                    <p className="text-sm-app text-slate-600">{action.desc}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'payments' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">💳</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Payment Records</h3>
            <p className="text-base-app text-slate-600 mb-6">
              View and manage all payment transactions and receipts
            </p>
            <button className="btn-primary">View Payments</button>
          </div>
        )}

        {activeTab === 'dues' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">⚠️</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Outstanding Dues</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Track pending payments and send reminders to parents
            </p>
            <button className="btn-primary">View Dues</button>
          </div>
        )}

        {activeTab === 'structure' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📋</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Fee Structure</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Manage fee categories, amounts, and payment schedules
            </p>
            <button className="btn-primary">Manage Structure</button>
          </div>
        )}

        {activeTab === 'reports' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📊</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Financial Reports</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Generate detailed financial reports and analytics
            </p>
            <button className="btn-primary">Generate Report</button>
          </div>
        )}
      </div>
    </div>
  );
}
