// src/app/academic-management/layout.tsx
import { Metadata } from 'next';
import AppLayout from '@/components/layout/app-layout';

export const metadata: Metadata = {
  title: 'Academic Management - EduPro',
  description: 'Manage curriculum, subjects, classes, and academic planning',
};

interface AcademicManagementLayoutProps {
  children: React.ReactNode;
}

export default function AcademicManagementLayout({ children }: AcademicManagementLayoutProps) {
  return (
    <AppLayout>
      {children}
    </AppLayout>
  );
}
