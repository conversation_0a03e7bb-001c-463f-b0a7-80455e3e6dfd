// src/constants/database.ts
/**
 * Centralized database schema definitions and constants
 * Generated from actual Supabase database schema
 * Single source of truth for all table names, column names, and schema definitions
 */

export const DATABASE_TABLES = {
  // Core tables
  STUDENTS: 'students',
  GUARDIANS: 'guardians',
  DOCUMENTS: 'documents',
  ACADEMIC_RECORDS: 'academic_records',

  // Master data tables
  CLASSES: 'classes',
  SECTIONS: 'sections',
  ACADEMIC_YEARS: 'academic_years',
  GUARDIAN_RELATIONS: 'guardian_relations',

  // Authentication tables
  PROFILES: 'profiles',

  // Academic management tables
  ENROLLMENTS: 'enrollments',
  TEACHERS: 'teachers',
  SUBJECTS: 'subjects',

  // Tracking tables
  ATTENDANCE: 'attendance',
  GRADES: 'grades',
} as const;

export const DATABASE_COLUMNS = {
  // Common columns across tables
  COMMON: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    IS_ACTIVE: 'is_active'
  },

  // profiles table columns
  PROFILES: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    EMAIL: 'email',
    FULL_NAME: 'full_name',
    ROLE: 'role',
  },

  // students table columns
  STUDENTS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    FIRST_NAME: 'first_name',
    LAST_NAME: 'last_name',
    DATE_OF_BIRTH: 'date_of_birth',
    GENDER: 'gender',
    EMAIL: 'email',
    PHONE_NUMBER: 'phone_number',
    ADDRESS: 'address',
    CLASS_ID: 'class_id',
    SECTION_ID: 'section_id',
    ACADEMIC_YEAR_ID: 'academic_year_id',
    IS_ACTIVE: 'is_active',
  },

  // classes table columns
  CLASSES: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    NAME: 'name',
    DESCRIPTION: 'description',
    GRADE_LEVEL: 'grade_level',
    IS_ACTIVE: 'is_active',
  },

  // sections table columns
  SECTIONS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    NAME: 'name',
    CLASS_ID: 'class_id',
    MAX_CAPACITY: 'max_capacity',
    IS_ACTIVE: 'is_active',
  },

  // academic_years table columns
  ACADEMIC_YEARS: {
    ID: 'id',
    TENANT_ID: 'tenant_id',
    NAME: 'name',
    START_DATE: 'start_date',
    END_DATE: 'end_date',
    IS_CURRENT: 'is_current',
    IS_ACTIVE: 'is_active',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },

  // guardian_relations table columns
  GUARDIAN_RELATIONS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    NAME: 'name',
    IS_ACTIVE: 'is_active',
  },

  // guardians table columns
  GUARDIANS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    STUDENT_ID: 'student_id',
    NAME: 'name',
    RELATION_ID: 'relation_id',
    PHONE: 'phone',
    EMAIL: 'email',
    ADDRESS: 'address',
    IS_PRIMARY: 'is_primary',
    IS_ACTIVE: 'is_active',
  },

  // documents table columns
  DOCUMENTS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    STUDENT_ID: 'student_id',
    TYPE: 'type',
    FILE_NAME: 'file_name',
    FILE_PATH: 'file_path',
    FILE_URL: 'file_url',
    FILE_SIZE: 'file_size',
    MIME_TYPE: 'mime_type',
    IS_REQUIRED: 'is_required',
    STATUS: 'status',
    IS_ACTIVE: 'is_active',
  },

  // academic_records table columns
  ACADEMIC_RECORDS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
    STUDENT_ID: 'student_id',
    CLASS_ID: 'class_id',
    SECTION_ID: 'section_id',
    ACADEMIC_YEAR_ID: 'academic_year_id',
    ROLL_NUMBER: 'roll_number',
    PREVIOUS_SCHOOL: 'previous_school',
    PREVIOUS_PERCENTAGE: 'previous_percentage',
    IS_ACTIVE: 'is_active',
  },

  // enrollments table columns
  ENROLLMENTS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },

  // teachers table columns
  TEACHERS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },

  // subjects table columns
  SUBJECTS: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },

  // attendance table columns
  ATTENDANCE: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },

  // grades table columns
  GRADES: {
    ID: 'id',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at',
  },

} as const;

/**
 * TypeScript type definitions based on actual database schema
 */

export interface ProfilesEntity {
  id: string;
  created_at: string;
  updated_at: string;
  email: string;
  full_name?: string;
  role: string;
}

export interface StudentsEntity {
  id: string;
  created_at: string;
  updated_at: string;
  first_name: string;
  last_name: string;
  date_of_birth?: string;
  gender?: string;
  email?: string;
  phone_number?: string;
  address?: string;
  class_id?: string;
  section_id?: string;
  academic_year_id?: string;
  is_active: boolean;
}

export interface ClassesEntity {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  description?: string;
  grade_level?: number;
  is_active: boolean;
}

export interface SectionsEntity {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  class_id: string;
  max_capacity?: number;
  is_active: boolean;
}

export interface AcademicYearsEntity {
  id?: string;
  tenant_id?: string;
  name?: string;
  start_date?: string;
  end_date?: string;
  is_current?: boolean;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface GuardianRelationsEntity {
  id: string;
  created_at: string;
  updated_at: string;
  name: string;
  is_active: boolean;
}

export interface GuardiansEntity {
  id: string;
  created_at: string;
  updated_at: string;
  student_id: string;
  name: string;
  relation_id: string;
  phone?: string;
  email?: string;
  address?: string;
  is_primary: boolean;
  is_active: boolean;
}

export interface DocumentsEntity {
  id: string;
  created_at: string;
  updated_at: string;
  student_id: string;
  type: string;
  file_name: string;
  file_path: string;
  file_url?: string;
  file_size?: number;
  mime_type?: string;
  is_required: boolean;
  status: string;
  is_active: boolean;
}

export interface AcademicRecordsEntity {
  id: string;
  created_at: string;
  updated_at: string;
  student_id: string;
  class_id: string;
  section_id?: string;
  academic_year_id: string;
  roll_number?: string;
  previous_school?: string;
  previous_percentage?: number;
  is_active: boolean;
}

export interface EnrollmentsEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface TeachersEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface SubjectsEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface AttendanceEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface GradesEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

/**
 * Insert and Update type definitions for database operations
 */

// Student types
export type StudentInsert = Omit<StudentsEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type StudentUpdate = Partial<StudentInsert>;

// Guardian types
export type GuardianInsert = Omit<GuardiansEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type GuardianUpdate = Partial<GuardianInsert>;

// Academic Record types
export type AcademicRecordInsert = Omit<AcademicRecordsEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type AcademicRecordUpdate = Partial<AcademicRecordInsert>;

// Document types
export type DocumentInsert = Omit<DocumentsEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type DocumentUpdate = Partial<DocumentInsert>;

// Class types
export type ClassInsert = Omit<ClassesEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type ClassUpdate = Partial<ClassInsert>;

// Section types
export type SectionInsert = Omit<SectionsEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type SectionUpdate = Partial<SectionInsert>;

// Academic Year types
export type AcademicYearInsert = Omit<AcademicYearsEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type AcademicYearUpdate = Partial<AcademicYearInsert>;

// Guardian Relation types
export type GuardianRelationInsert = Omit<GuardianRelationsEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type GuardianRelationUpdate = Partial<GuardianRelationInsert>;

// Profile types
export type ProfileInsert = Omit<ProfilesEntity, 'id' | 'created_at' | 'updated_at'> & {
  id?: string;
  created_at?: string;
  updated_at?: string;
};

export type ProfileUpdate = Partial<ProfileInsert>;

// Alias types for backward compatibility
export type StudentEntity = StudentsEntity;
export type GuardianEntity = GuardiansEntity;
export type AcademicRecordEntity = AcademicRecordsEntity;
export type DocumentEntity = DocumentsEntity;
export type ClassEntity = ClassesEntity;
export type SectionEntity = SectionsEntity;
export type AcademicYearEntity = AcademicYearsEntity;
export type GuardianRelationEntity = GuardianRelationsEntity;
export type ProfileEntity = ProfilesEntity;

/**
 * Validation rules based on actual schema
 */
export const VALIDATION_RULES = {
  STUDENTS: {
    FIRST_NAME: { required: true },
    LAST_NAME: { required: true },
    IS_ACTIVE: { required: true },
  },
  GUARDIANS: {
    STUDENT_ID: { required: true },
    NAME: { required: true },
    RELATION_ID: { required: true },
    IS_PRIMARY: { required: true },
    IS_ACTIVE: { required: true },
  },
  CLASSES: {
    NAME: { required: true },
    IS_ACTIVE: { required: true },
  },
  SECTIONS: {
    NAME: { required: true },
    CLASS_ID: { required: true },
    IS_ACTIVE: { required: true },
  },
} as const;

/**
 * Database configuration constants
 */
export const DATABASE_CONFIG = {
  // Schema settings
  SCHEMA: 'public',
  
  // Pagination settings
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Connection settings
  CONNECTION_TIMEOUT: 30000, // 30 seconds
  QUERY_TIMEOUT: 60000, // 60 seconds
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // Session settings
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  
  // File upload settings
  STORAGE_BUCKET: 'student-documents',
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ],
  
  // Status enums
  DOCUMENT_STATUS: {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected'
  } as const,
  
  GENDER_OPTIONS: {
    MALE: 'male',
    FEMALE: 'female',
    OTHER: 'other'
  } as const
} as const;

/**
 * Helper functions for schema management
 */
export const getTableName = (tableKey: keyof typeof DATABASE_TABLES): string => {
  return DATABASE_TABLES[tableKey];
};

export const getColumnName = (table: keyof typeof DATABASE_COLUMNS, column: string): string => {
  const tableColumns = DATABASE_COLUMNS[table];
  if (!tableColumns || !(column in tableColumns)) {
    throw new Error(`Column ${column} not found in table ${table}`);
  }
  return (tableColumns as any)[column];
};

// Type exports for external use
export type DatabaseTableName = typeof DATABASE_TABLES[keyof typeof DATABASE_TABLES];
export type DatabaseColumnName = string;

// Schema version for migration tracking
export const SCHEMA_VERSION = '2.0.0';
export const LAST_UPDATED = '2025-06-25T09:08:45.534Z';
