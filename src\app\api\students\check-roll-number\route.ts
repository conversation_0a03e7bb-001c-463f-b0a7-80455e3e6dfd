// src/app/api/students/check-roll-number/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { MasterDataService } from '../../../../services/masterDataService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const { rollNumber, classId, sectionId, academicYearId, excludeStudentId } = body;

    if (!rollNumber || !classId || !sectionId || !academicYearId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Roll number, class ID, section ID, and academic year ID are required' 
        },
        { status: 400 }
      );
    }

    const exists = await MasterDataService.isRollNumberExists(
      rollNumber,
      classId,
      sectionId,
      academicYearId,
      excludeStudentId
    );

    return NextResponse.json({
      success: true,
      data: {
        exists,
        available: !exists
      }
    });
  } catch (error) {
    console.error('Error in POST /api/students/check-roll-number:', error);
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to check roll number'
      },
      { status: 500 }
    );
  }
}
