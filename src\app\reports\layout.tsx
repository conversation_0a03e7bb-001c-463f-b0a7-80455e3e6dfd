// src/app/reports/layout.tsx
import { Metadata } from 'next';
import AppLayout from '@/components/layout/app-layout';

export const metadata: Metadata = {
  title: 'Reports - EduPro',
  description: 'Generate and view comprehensive school reports and analytics',
};

interface ReportsLayoutProps {
  children: React.ReactNode;
}

export default function ReportsLayout({ children }: ReportsLayoutProps) {
  return (
    <AppLayout>
      {children}
    </AppLayout>
  );
}
