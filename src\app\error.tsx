'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorPage({ error, reset }: ErrorPageProps) {
  const router = useRouter();

  useEffect(() => {
    // Log error for debugging (in production, send to monitoring service)
    console.error('Application error:', {
      message: error.message,
      digest: error.digest,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });
  }, [error]);

  // Determine error type and appropriate messaging
  const getErrorInfo = () => {
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('fetch')) {
      return {
        title: 'Connection Issue',
        description: 'We\'re having trouble connecting to our servers. This is usually temporary and should resolve shortly.',
        type: 'network'
      };
    }
    
    if (message.includes('auth') || message.includes('unauthorized')) {
      return {
        title: 'Authentication Required',
        description: 'Your session may have expired. Please sign in again to continue using EduPro.',
        type: 'auth'
      };
    }
    
    if (message.includes('permission') || message.includes('forbidden')) {
      return {
        title: 'Access Restricted',
        description: 'You don\'t have permission to access this feature. Contact your administrator if you believe this is an error.',
        type: 'permission'
      };
    }
    
    // Default error
    return {
      title: 'Something Went Wrong',
      description: 'We encountered an unexpected issue with this page. The rest of EduPro is still working normally.',
      type: 'general'
    };
  };

  const errorInfo = getErrorInfo();

  const handleGoToDashboard = () => {
    router.push('/dashboard');
  };

  const handleGoHome = () => {
    router.push('/');
  };

  const handleContactSupport = () => {
    // In a real application, this would open a support ticket or email
    const subject = encodeURIComponent(`EduPro Error Report - ${errorInfo.title}`);
    const body = encodeURIComponent(
      `Error Details:\n\nType: ${errorInfo.type}\nMessage: ${error.message}\nDigest: ${error.digest || 'N/A'}\nTimestamp: ${new Date().toISOString()}\n\nPlease describe what you were doing when this error occurred:`
    );
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="card-lg max-w-2xl w-full text-center">
        {/* Error Icon */}
        <div className="mb-6">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg 
              className="w-8 h-8 text-red-600" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 15.5c-.77.833.192 2.5 1.732 2.5z" 
              />
            </svg>
          </div>
          
          {/* EduPro Logo/Brand */}
          <div className="flex items-center justify-center mb-4">
            <div className="w-8 h-8 bg-indigo-600 rounded-lg flex items-center justify-center mr-3">
              <span className="text-white font-bold text-sm-app">E</span>
            </div>
            <span className="text-lg-app font-semibold text-gray-900">EduPro</span>
          </div>
        </div>

        {/* Error Content */}
        <div className="mb-8">
          <h1 className="section-header-md mb-4">{errorInfo.title}</h1>
          <p className="card-content text-gray-600 mb-6 leading-relaxed">
            {errorInfo.description}
          </p>
          
          {/* Technical Details (collapsible) */}
          <details className="text-left bg-gray-50 rounded-lg p-4 mb-6">
            <summary className="text-sm-app font-medium text-gray-700 cursor-pointer hover:text-gray-900">
              Technical Details
            </summary>
            <div className="mt-3 text-xs-app text-gray-600 font-mono bg-white p-3 rounded border">
              <div><strong>Error:</strong> {error.message}</div>
              {error.digest && <div><strong>ID:</strong> {error.digest}</div>}
              <div><strong>Time:</strong> {new Date().toLocaleString()}</div>
            </div>
          </details>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={reset}
              className="btn-primary flex items-center justify-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span>Try Again</span>
            </button>
            
            <button
              onClick={handleGoToDashboard}
              className="btn-secondary flex items-center justify-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
              </svg>
              <span>Go to Dashboard</span>
            </button>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={handleGoHome}
              className="btn-outline flex items-center justify-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
              </svg>
              <span>Return Home</span>
            </button>
            
            <button
              onClick={handleContactSupport}
              className="btn-outline flex items-center justify-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 109.75 9.75A9.75 9.75 0 0012 2.25z" />
              </svg>
              <span>Contact Support</span>
            </button>
          </div>
        </div>

        {/* Reassurance Message */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm-app text-gray-500">
            <strong>Good news:</strong> This is an isolated issue. The rest of EduPro is working normally and your data is safe.
          </p>
        </div>
      </div>
    </div>
  );
}
