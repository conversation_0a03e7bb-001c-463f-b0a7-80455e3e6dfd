// src/repositories/academicRecordRepository.ts
import { SupabaseClient } from '@supabase/supabase-js';
import {
  AcademicRecordEntity,
  AcademicRecordInsert,
  AcademicRecordUpdate,
  DATABASE_COLUMNS,
  DATABASE_TABLES
} from '../constants/database';
import { <PERSON><PERSON>r<PERSON><PERSON>, ErrorHandler, ServiceError } from '../services/core/errorHandler';
import { Database } from '../types/database';
import { BaseRepository, PaginatedResult, PaginationOptions } from './baseRepository';

/**
 * Academic record-specific repository interface
 */
export interface IAcademicRecordRepository {
  // Basic CRUD operations
  create(data: AcademicRecordInsert): Promise<AcademicRecordEntity>;
  findById(id: string): Promise<AcademicRecordEntity | null>;
  update(id: string, data: AcademicRecordUpdate): Promise<AcademicRecordEntity>;
  softDelete(id: string): Promise<void>;

  // Academic record-specific methods
  findByStudentId(studentId: string): Promise<AcademicRecordEntity | null>;
  findByClassSection(classId: string, sectionId: string, academicYearId: string, pagination?: PaginationOptions): Promise<PaginatedResult<AcademicRecordEntity>>;
  findByRollNumber(rollNumber: string, classId: string, sectionId: string, academicYearId: string): Promise<AcademicRecordEntity | null>;
  isRollNumberUnique(rollNumber: string, classId: string, sectionId: string, academicYearId: string, excludeStudentId?: string): Promise<boolean>;
  getClassCapacity(classId: string, sectionId: string, academicYearId: string): Promise<{ current: number; maximum: number }>;
  findWithRelations(studentId: string): Promise<any>;
  findByAcademicYear(academicYearId: string, pagination?: PaginationOptions): Promise<PaginatedResult<AcademicRecordEntity>>;
}

/**
 * Academic record repository implementation
 * Handles all database operations related to academic records
 */
export class AcademicRecordRepository extends BaseRepository<AcademicRecordEntity, AcademicRecordInsert, AcademicRecordUpdate> implements IAcademicRecordRepository {
  
  constructor(client: SupabaseClient<Database>) {
    super(client, DATABASE_TABLES.ACADEMIC_RECORDS);
  }

  /**
   * Find academic record by student ID
   * @param studentId - Student ID
   * @returns Academic record entity or null
   */
  async findByStudentId(studentId: string): Promise<AcademicRecordEntity | null> {
    try {
      this.log('info', 'Finding academic record by student ID', { studentId });

      const record = await this.findOne({
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.STUDENT_ID]: studentId
      });

      this.log('info', 'Academic record found by student ID', { studentId, found: !!record });
      return record;
    } catch (error) {
      this.log('error', 'Error finding academic record by student ID', { error, studentId });
      throw ErrorHandler.handle(error, 'Find academic record by student ID');
    }
  }

  /**
   * Find academic records by class and section
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @param pagination - Pagination options
   * @returns Paginated list of academic records
   */
  async findByClassSection(
    classId: string,
    sectionId: string,
    academicYearId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<AcademicRecordEntity>> {
    try {
      this.log('info', 'Finding academic records by class and section', { classId, sectionId, academicYearId });

      const filters = {
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.CLASS_ID]: classId,
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.SECTION_ID]: sectionId,
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.ACADEMIC_YEAR_ID]: academicYearId
      };

      const result = await this.findAll(filters, {
        ...pagination,
        sortBy: DATABASE_COLUMNS.ACADEMIC_RECORDS.ROLL_NUMBER,
        sortOrder: 'asc'
      });

      this.log('info', 'Academic records found by class and section', { 
        classId, 
        sectionId, 
        academicYearId, 
        count: result.data.length 
      });

      return result;
    } catch (error) {
      this.log('error', 'Error finding academic records by class and section', { error, classId, sectionId });
      throw ErrorHandler.handle(error, 'Find academic records by class and section');
    }
  }

  /**
   * Find academic record by roll number
   * @param rollNumber - Roll number
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @returns Academic record entity or null
   */
  async findByRollNumber(
    rollNumber: string,
    classId: string,
    sectionId: string,
    academicYearId: string
  ): Promise<AcademicRecordEntity | null> {
    try {
      this.log('info', 'Finding academic record by roll number', { rollNumber, classId, sectionId, academicYearId });

      const record = await this.findOne({
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.ROLL_NUMBER]: rollNumber,
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.CLASS_ID]: classId,
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.SECTION_ID]: sectionId,
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.ACADEMIC_YEAR_ID]: academicYearId
      });

      this.log('info', 'Academic record found by roll number', { rollNumber, found: !!record });
      return record;
    } catch (error) {
      this.log('error', 'Error finding academic record by roll number', { error, rollNumber });
      throw ErrorHandler.handle(error, 'Find academic record by roll number');
    }
  }

  /**
   * Check if roll number is unique within class, section, and academic year
   * @param rollNumber - Roll number to check
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @param excludeStudentId - Student ID to exclude from check
   * @returns True if roll number is unique
   */
  async isRollNumberUnique(
    rollNumber: string,
    classId: string,
    sectionId: string,
    academicYearId: string,
    excludeStudentId?: string
  ): Promise<boolean> {
    try {
      this.log('info', 'Checking roll number uniqueness', { 
        rollNumber, 
        classId, 
        sectionId, 
        academicYearId, 
        excludeStudentId 
      });

      let query = this.client
        .from(this.tableName)
        .select(DATABASE_COLUMNS.ACADEMIC_RECORDS.STUDENT_ID)
        .eq(DATABASE_COLUMNS.ACADEMIC_RECORDS.ROLL_NUMBER, rollNumber)
        .eq(DATABASE_COLUMNS.ACADEMIC_RECORDS.CLASS_ID, classId)
        .eq(DATABASE_COLUMNS.ACADEMIC_RECORDS.SECTION_ID, sectionId)
        .eq(DATABASE_COLUMNS.ACADEMIC_RECORDS.ACADEMIC_YEAR_ID, academicYearId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (excludeStudentId) {
        query = query.neq(DATABASE_COLUMNS.ACADEMIC_RECORDS.STUDENT_ID, excludeStudentId);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Check roll number uniqueness');
      }

      const isUnique = data === null;
      this.log('info', 'Roll number uniqueness checked', { rollNumber, isUnique });
      return isUnique;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return true;
      }
      this.log('error', 'Error checking roll number uniqueness', { error, rollNumber });
      throw error;
    }
  }

  /**
   * Get current enrollment count and maximum capacity for a class section
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @returns Current and maximum capacity
   */
  async getClassCapacity(
    classId: string,
    sectionId: string,
    academicYearId: string
  ): Promise<{ current: number; maximum: number }> {
    try {
      this.log('info', 'Getting class capacity', { classId, sectionId, academicYearId });

      // Get current enrollment count
      const currentCount = await this.count({
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.CLASS_ID]: classId,
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.SECTION_ID]: sectionId,
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.ACADEMIC_YEAR_ID]: academicYearId
      });

      // Get maximum capacity from sections table
      const { data: sectionData, error: sectionError } = await this.client
        .from(DATABASE_TABLES.SECTIONS)
        .select(DATABASE_COLUMNS.SECTIONS.MAX_CAPACITY)
        .eq(DATABASE_COLUMNS.SECTIONS.ID, sectionId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .single();

      if (sectionError) {
        throw ErrorHandler.handle(sectionError, 'Get section capacity');
      }

      const maxCapacity = sectionData?.max_capacity || 50; // Default capacity

      this.log('info', 'Class capacity retrieved', { 
        classId, 
        sectionId, 
        academicYearId, 
        current: currentCount, 
        maximum: maxCapacity 
      });

      return {
        current: currentCount,
        maximum: maxCapacity
      };
    } catch (error) {
      this.log('error', 'Error getting class capacity', { error, classId, sectionId });
      throw ErrorHandler.handle(error, 'Get class capacity');
    }
  }

  /**
   * Find academic record with related class, section, and academic year information
   * @param studentId - Student ID
   * @returns Academic record with relations
   */
  async findWithRelations(studentId: string): Promise<any> {
    try {
      this.log('info', 'Finding academic record with relations', { studentId });

      const { data, error } = await this.client
        .from(this.tableName)
        .select(`
          *,
          class:${DATABASE_TABLES.CLASSES}(${DATABASE_COLUMNS.CLASSES.ID}, ${DATABASE_COLUMNS.CLASSES.NAME}),
          section:${DATABASE_TABLES.SECTIONS}(${DATABASE_COLUMNS.SECTIONS.ID}, ${DATABASE_COLUMNS.SECTIONS.NAME}),
          academic_year:${DATABASE_TABLES.ACADEMIC_YEARS}(${DATABASE_COLUMNS.ACADEMIC_YEARS.ID}, ${DATABASE_COLUMNS.ACADEMIC_YEARS.YEAR})
        `)
        .eq(DATABASE_COLUMNS.ACADEMIC_RECORDS.STUDENT_ID, studentId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        throw ErrorHandler.handle(error, 'Find academic record with relations');
      }

      this.log('info', 'Academic record with relations found', { studentId, found: !!data });
      return data;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return null;
      }
      this.log('error', 'Error finding academic record with relations', { error, studentId });
      throw ErrorHandler.handle(error, 'Find academic record with relations');
    }
  }

  /**
   * Find academic records by academic year
   * @param academicYearId - Academic year ID
   * @param pagination - Pagination options
   * @returns Paginated list of academic records
   */
  async findByAcademicYear(
    academicYearId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<AcademicRecordEntity>> {
    try {
      this.log('info', 'Finding academic records by academic year', { academicYearId });

      const filters = {
        [DATABASE_COLUMNS.ACADEMIC_RECORDS.ACADEMIC_YEAR_ID]: academicYearId
      };

      const result = await this.findAll(filters, pagination);

      this.log('info', 'Academic records found by academic year', { 
        academicYearId, 
        count: result.data.length 
      });

      return result;
    } catch (error) {
      this.log('error', 'Error finding academic records by academic year', { error, academicYearId });
      throw ErrorHandler.handle(error, 'Find academic records by academic year');
    }
  }

  /**
   * Override create method to add academic record-specific validation
   */
  async create(data: AcademicRecordInsert): Promise<AcademicRecordEntity> {
    try {
      this.log('info', 'Creating academic record', { 
        studentId: data.student_id, 
        rollNumber: data.roll_number 
      });

      // Validate required fields
      this.validateEntity(data, [
        'student_id',
        'class_id',
        'section_id',
        'academic_year_id',
        'roll_number',
        'admission_date'
      ]);

      // Validate roll number uniqueness
      const isRollNumberUnique = await this.isRollNumberUnique(
        data.roll_number,
        data.class_id,
        data.section_id,
        data.academic_year_id
      );

      if (!isRollNumberUnique) {
        throw new ServiceError(
          ErrorCode.ROLL_NUMBER_TAKEN,
          'Roll number already exists in this class and section',
          null,
          'Create academic record'
        );
      }

      // Check class capacity
      const capacity = await this.getClassCapacity(
        data.class_id,
        data.section_id,
        data.academic_year_id
      );

      if (capacity.current >= capacity.maximum) {
        throw new ServiceError(
          ErrorCode.ENROLLMENT_LIMIT_EXCEEDED,
          'Class section is at full capacity',
          null,
          'Create academic record'
        );
      }

      const record = await super.create(data);
      this.log('info', 'Academic record created successfully', { 
        recordId: record.id, 
        studentId: data.student_id 
      });
      return record;
    } catch (error) {
      this.log('error', 'Error creating academic record', { error, data });
      throw ErrorHandler.handle(error, 'Create academic record');
    }
  }

  /**
   * Override update method to add academic record-specific validation
   */
  async update(id: string, data: AcademicRecordUpdate): Promise<AcademicRecordEntity> {
    try {
      this.log('info', 'Updating academic record', { recordId: id });

      // Validate roll number uniqueness if being updated
      if (data.roll_number && data.class_id && data.section_id && data.academic_year_id) {
        const existingRecord = await this.findById(id);
        if (existingRecord) {
          const isRollNumberUnique = await this.isRollNumberUnique(
            data.roll_number,
            data.class_id,
            data.section_id,
            data.academic_year_id,
            existingRecord.student_id
          );

          if (!isRollNumberUnique) {
            throw new ServiceError(
              ErrorCode.ROLL_NUMBER_TAKEN,
              'Roll number already exists in this class and section',
              null,
              'Update academic record'
            );
          }
        }
      }

      const record = await super.update(id, data);
      this.log('info', 'Academic record updated successfully', { recordId: id });
      return record;
    } catch (error) {
      this.log('error', 'Error updating academic record', { error, recordId: id });
      throw ErrorHandler.handle(error, 'Update academic record');
    }
  }
}
