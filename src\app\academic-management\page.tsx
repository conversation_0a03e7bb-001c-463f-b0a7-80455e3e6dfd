// src/app/academic-management/page.tsx
'use client';

import { useState } from 'react';

export default function AcademicManagementPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl-app font-bold text-slate-900">Academic Management</h1>
          <p className="text-base-app text-slate-600 mt-1">
            Manage curriculum, subjects, classes, and academic planning
          </p>
        </div>
        <button className="btn-primary">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Subject
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-slate-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: '📚' },
            { id: 'curriculum', label: 'Curriculum', icon: '📖' },
            { id: 'subjects', label: 'Subjects', icon: '📝' },
            { id: 'classes', label: 'Classes', icon: '🏫' },
            { id: 'timetable', label: 'Timetable', icon: '⏰' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm-app transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h2 className="text-xl-app font-semibold text-slate-900">Academic Overview</h2>
            
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Total Classes', value: '24', change: '+2', color: 'blue' },
                { title: 'Subjects', value: '18', change: '+1', color: 'green' },
                { title: 'Academic Year', value: '2024-25', change: 'Current', color: 'purple' },
                { title: 'Semesters', value: '2', change: 'Active', color: 'orange' },
              ].map((stat, index) => (
                <div key={index} className="bg-slate-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm-app font-medium text-slate-600">{stat.title}</p>
                      <p className="text-2xl-app font-bold text-slate-900">{stat.value}</p>
                    </div>
                    <div className="text-sm-app font-medium text-slate-500">
                      {stat.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            <div>
              <h3 className="text-lg-app font-medium text-slate-900 mb-4">Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { title: 'Create New Class', icon: '🏫', desc: 'Set up a new class with sections' },
                  { title: 'Add Subject', icon: '📝', desc: 'Add new subject to curriculum' },
                  { title: 'Generate Timetable', icon: '⏰', desc: 'Create class schedules' },
                ].map((action, index) => (
                  <div key={index} className="border border-slate-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer">
                    <div className="text-2xl mb-2">{action.icon}</div>
                    <h4 className="text-base-app font-medium text-slate-900">{action.title}</h4>
                    <p className="text-sm-app text-slate-600">{action.desc}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'curriculum' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📖</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Curriculum Management</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Design and manage academic curriculum and learning objectives
            </p>
            <button className="btn-primary">Create Curriculum</button>
          </div>
        )}

        {activeTab === 'subjects' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📝</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Subject Management</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Add, edit, and organize subjects across different classes
            </p>
            <button className="btn-primary">Add Subject</button>
          </div>
        )}

        {activeTab === 'classes' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🏫</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Class Management</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Organize students into classes and manage class structures
            </p>
            <button className="btn-primary">Create Class</button>
          </div>
        )}

        {activeTab === 'timetable' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">⏰</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Timetable Management</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Create and manage class schedules and teacher assignments
            </p>
            <button className="btn-primary">Generate Timetable</button>
          </div>
        )}
      </div>
    </div>
  );
}
