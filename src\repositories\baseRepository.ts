// src/repositories/baseRepository.ts
import { SupabaseClient } from '@supabase/supabase-js';
import { Database } from '../types/database';
import { ServiceError, ErrorCode, ErrorHandler } from '../services/core/errorHandler';
import { BaseEntity, DATABASE_COLUMNS } from '../constants/database';

/**
 * Repository interface for type-safe database operations
 */
export interface IRepository<TEntity extends BaseEntity, TInsert = Partial<TEntity>, TUpdate = Partial<TEntity>> {
  findById(id: string): Promise<TEntity | null>;
  findAll(filters?: Record<string, any>, pagination?: PaginationOptions): Promise<PaginatedResult<TEntity>>;
  findOne(filters: Record<string, any>): Promise<TEntity | null>;
  create(data: TInsert): Promise<TEntity>;
  update(id: string, data: TUpdate): Promise<TEntity>;
  delete(id: string): Promise<void>;
  softDelete(id: string): Promise<void>;
  exists(id: string): Promise<boolean>;
  count(filters?: Record<string, any>): Promise<number>;
  batchCreate(data: TInsert[]): Promise<TEntity[]>;
  batchUpdate(updates: Array<{ id: string; data: TUpdate }>): Promise<TEntity[]>;
}

/**
 * Pagination options interface
 */
export interface PaginationOptions {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Paginated result interface
 */
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

/**
 * Query builder interface for complex queries
 */
export interface QueryBuilder<T> {
  select(columns?: string[]): QueryBuilder<T>;
  where(column: string, operator: string, value: any): QueryBuilder<T>;
  whereIn(column: string, values: any[]): QueryBuilder<T>;
  whereNull(column: string): QueryBuilder<T>;
  whereNotNull(column: string): QueryBuilder<T>;
  orderBy(column: string, direction?: 'asc' | 'desc'): QueryBuilder<T>;
  limit(count: number): QueryBuilder<T>;
  offset(count: number): QueryBuilder<T>;
  join(table: string, condition: string): QueryBuilder<T>;
  execute(): Promise<T[]>;
  first(): Promise<T | null>;
  count(): Promise<number>;
}

/**
 * Abstract base repository class providing common database operations
 * All specific repositories should extend this class
 */
export abstract class BaseRepository<TEntity extends BaseEntity, TInsert = Partial<TEntity>, TUpdate = Partial<TEntity>>
  implements IRepository<TEntity, TInsert, TUpdate> {
  
  protected client: SupabaseClient<Database>;
  protected tableName: string;

  constructor(client: SupabaseClient<Database>, tableName: string) {
    this.client = client;
    this.tableName = tableName;
  }

  /**
   * Find entity by ID
   */
  async findById(id: string): Promise<TEntity | null> {
    try {
      const { data, error } = await this.client
        .from(this.tableName as any)
        .select('*')
        .eq(DATABASE_COLUMNS.COMMON.ID, id)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        throw ErrorHandler.handle(error, `Find ${this.tableName} by ID`);
      }

      return data as TEntity;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return null;
      }
      throw ErrorHandler.handle(error, `Find ${this.tableName} by ID`);
    }
  }

  /**
   * Find all entities with optional filtering and pagination
   */
  async findAll(
    filters: Record<string, any> = {},
    pagination: PaginationOptions = {}
  ): Promise<PaginatedResult<TEntity>> {
    try {
      const {
        page = 1,
        pageSize = 20,
        sortBy = DATABASE_COLUMNS.COMMON.CREATED_AT,
        sortOrder = 'desc'
      } = pagination;

      // Build base query
      let query = this.client
        .from(this.tableName as any)
        .select('*', { count: 'exact' })
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' });

      // Apply pagination
      const from = (page - 1) * pageSize;
      const to = from + pageSize - 1;
      query = query.range(from, to);

      const { data, error, count } = await query;

      if (error) {
        throw ErrorHandler.handle(error, `Find all ${this.tableName}`);
      }

      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / pageSize);

      return {
        data: (data || []) as TEntity[],
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };
    } catch (error) {
      throw ErrorHandler.handle(error, `Find all ${this.tableName}`);
    }
  }

  /**
   * Find single entity by filters
   */
  async findOne(filters: Record<string, any>): Promise<TEntity | null> {
    try {
      let query = this.client
        .from(this.tableName as any)
        .select('*')
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });

      const { data, error } = await query.single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Not found
        }
        throw ErrorHandler.handle(error, `Find one ${this.tableName}`);
      }

      return data as TEntity;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return null;
      }
      throw ErrorHandler.handle(error, `Find one ${this.tableName}`);
    }
  }

  /**
   * Create new entity
   */
  async create(data: TInsert): Promise<TEntity> {
    try {
      const insertData = {
        ...data,
        [DATABASE_COLUMNS.COMMON.CREATED_AT]: new Date().toISOString(),
        [DATABASE_COLUMNS.COMMON.UPDATED_AT]: new Date().toISOString(),
        [DATABASE_COLUMNS.COMMON.IS_ACTIVE]: true
      };

      const { data: result, error } = await this.client
        .from(this.tableName as any)
        .insert(insertData)
        .select()
        .single();

      if (error) {
        throw ErrorHandler.handle(error, `Create ${this.tableName}`);
      }

      return result as TEntity;
    } catch (error) {
      throw ErrorHandler.handle(error, `Create ${this.tableName}`);
    }
  }

  /**
   * Update existing entity
   */
  async update(id: string, data: TUpdate): Promise<TEntity> {
    try {
      const updateData = {
        ...data,
        [DATABASE_COLUMNS.COMMON.UPDATED_AT]: new Date().toISOString()
      };

      const { data: result, error } = await this.client
        .from(this.tableName as any)
        .update(updateData)
        .eq(DATABASE_COLUMNS.COMMON.ID, id)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .select()
        .single();

      if (error) {
        throw ErrorHandler.handle(error, `Update ${this.tableName}`);
      }

      if (!result) {
        throw new ServiceError(
          ErrorCode.NOT_FOUND,
          `${this.tableName} with ID ${id} not found`,
          null,
          `Update ${this.tableName}`
        );
      }

      return result as TEntity;
    } catch (error) {
      throw ErrorHandler.handle(error, `Update ${this.tableName}`);
    }
  }

  /**
   * Hard delete entity
   */
  async delete(id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(this.tableName as any)
        .delete()
        .eq(DATABASE_COLUMNS.COMMON.ID, id);

      if (error) {
        throw ErrorHandler.handle(error, `Delete ${this.tableName}`);
      }
    } catch (error) {
      throw ErrorHandler.handle(error, `Delete ${this.tableName}`);
    }
  }

  /**
   * Soft delete entity (set is_active = false)
   */
  async softDelete(id: string): Promise<void> {
    try {
      const { error } = await this.client
        .from(this.tableName as any)
        .update({
          [DATABASE_COLUMNS.COMMON.IS_ACTIVE]: false,
          [DATABASE_COLUMNS.COMMON.UPDATED_AT]: new Date().toISOString()
        })
        .eq(DATABASE_COLUMNS.COMMON.ID, id);

      if (error) {
        throw ErrorHandler.handle(error, `Soft delete ${this.tableName}`);
      }
    } catch (error) {
      throw ErrorHandler.handle(error, `Soft delete ${this.tableName}`);
    }
  }

  /**
   * Check if entity exists
   */
  async exists(id: string): Promise<boolean> {
    try {
      const { data, error } = await this.client
        .from(this.tableName as any)
        .select(DATABASE_COLUMNS.COMMON.ID)
        .eq(DATABASE_COLUMNS.COMMON.ID, id)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, `Check ${this.tableName} existence`);
      }

      return data !== null;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return false;
      }
      throw error;
    }
  }

  /**
   * Count entities with optional filters
   */
  async count(filters: Record<string, any> = {}): Promise<number> {
    try {
      let query = this.client
        .from(this.tableName as any)
        .select('*', { count: 'exact', head: true })
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      // Apply filters
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          query = query.eq(key, value);
        }
      });

      const { count, error } = await query;

      if (error) {
        throw ErrorHandler.handle(error, `Count ${this.tableName}`);
      }

      return count || 0;
    } catch (error) {
      throw ErrorHandler.handle(error, `Count ${this.tableName}`);
    }
  }

  /**
   * Batch create multiple entities
   */
  async batchCreate(data: TInsert[]): Promise<TEntity[]> {
    try {
      const insertData = data.map(item => ({
        ...item,
        [DATABASE_COLUMNS.COMMON.CREATED_AT]: new Date().toISOString(),
        [DATABASE_COLUMNS.COMMON.UPDATED_AT]: new Date().toISOString(),
        [DATABASE_COLUMNS.COMMON.IS_ACTIVE]: true
      }));

      const { data: result, error } = await this.client
        .from(this.tableName as any)
        .insert(insertData)
        .select();

      if (error) {
        throw ErrorHandler.handle(error, `Batch create ${this.tableName}`);
      }

      return (result || []) as TEntity[];
    } catch (error) {
      throw ErrorHandler.handle(error, `Batch create ${this.tableName}`);
    }
  }

  /**
   * Batch update multiple entities
   */
  async batchUpdate(updates: Array<{ id: string; data: TUpdate }>): Promise<TEntity[]> {
    try {
      const results: TEntity[] = [];

      // Note: Supabase doesn't support batch updates directly
      // We'll perform individual updates in a transaction-like manner
      for (const update of updates) {
        const result = await this.update(update.id, update.data);
        results.push(result);
      }

      return results;
    } catch (error) {
      throw ErrorHandler.handle(error, `Batch update ${this.tableName}`);
    }
  }

  /**
   * Create a query builder for complex queries
   */
  protected createQueryBuilder(): any {
    return this.client.from(this.tableName as any);
  }

  /**
   * Execute raw SQL query (use with caution)
   */
  protected async executeRawQuery<T>(query: string, params: any[] = []): Promise<T[]> {
    try {
      const { data, error } = await this.client.rpc('execute_sql', {
        query,
        params
      });

      if (error) {
        throw ErrorHandler.handle(error, `Execute raw query on ${this.tableName}`);
      }

      return data || [];
    } catch (error) {
      throw ErrorHandler.handle(error, `Execute raw query on ${this.tableName}`);
    }
  }

  /**
   * Validate entity data before operations
   */
  protected validateEntity(data: Partial<TEntity>, requiredFields: (keyof TEntity)[] = []): void {
    const missingFields = requiredFields.filter(field => 
      data[field] === undefined || data[field] === null || data[field] === ''
    );

    if (missingFields.length > 0) {
      throw new ServiceError(
        ErrorCode.VALIDATION_ERROR,
        `Missing required fields: ${missingFields.join(', ')}`,
        null,
        `Validate ${this.tableName}`
      );
    }
  }

  /**
   * Log repository operations
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      repository: this.constructor.name,
      table: this.tableName,
      level,
      message,
      data
    };

    switch (level) {
      case 'error':
        console.error(logEntry);
        break;
      case 'warn':
        console.warn(logEntry);
        break;
      default:
        console.log(logEntry);
    }
  }
}
