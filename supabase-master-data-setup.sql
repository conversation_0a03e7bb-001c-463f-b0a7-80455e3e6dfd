-- Supabase Master Data Tables Setup for EduPro
-- Run this SQL in Supabase Dashboard > SQL Editor

-- 1. Create guardian_relations table
CREATE TABLE IF NOT EXISTS guardian_relations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create classes table
CREATE TABLE IF NOT EXISTS classes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(100) NOT NULL,
  description TEXT,
  grade_level INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create sections table
CREATE TABLE IF NOT EXISTS sections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(50) NOT NULL,
  class_id UUID REFERENCES classes(id),
  max_capacity INTEGER DEFAULT 30,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. Create academic_years table
CREATE TABLE IF NOT EXISTS academic_years (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  year VARCHAR(20) NOT NULL,
  start_date DATE,
  end_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create students table (if not exists)
CREATE TABLE IF NOT EXISTS students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  date_of_birth DATE,
  gender VARCHAR(20) CHECK (gender IN ('male', 'female', 'other')),
  email VARCHAR(255),
  phone_number VARCHAR(20),
  address TEXT,
  blood_group VARCHAR(10),
  nationality VARCHAR(100),
  religion VARCHAR(100),
  guardian_name VARCHAR(200),
  guardian_relation_id UUID REFERENCES guardian_relations(id),
  guardian_phone VARCHAR(20),
  guardian_email VARCHAR(255),
  guardian_address TEXT,
  emergency_contact VARCHAR(20),
  class_id UUID REFERENCES classes(id),
  section_id UUID REFERENCES sections(id),
  roll_number VARCHAR(20),
  academic_year_id UUID REFERENCES academic_years(id),
  previous_school VARCHAR(200),
  birth_certificate_url TEXT,
  previous_records_url TEXT,
  medical_records_url TEXT,
  photograph_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. Enable Row Level Security (RLS) for all tables
ALTER TABLE guardian_relations ENABLE ROW LEVEL SECURITY;
ALTER TABLE classes ENABLE ROW LEVEL SECURITY;
ALTER TABLE sections ENABLE ROW LEVEL SECURITY;
ALTER TABLE academic_years ENABLE ROW LEVEL SECURITY;
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policies for guardian_relations
DROP POLICY IF EXISTS "Allow read access to guardian_relations" ON guardian_relations;
CREATE POLICY "Allow read access to guardian_relations" ON guardian_relations
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow authenticated users to manage guardian_relations" ON guardian_relations;
CREATE POLICY "Allow authenticated users to manage guardian_relations" ON guardian_relations
  FOR ALL USING (auth.role() = 'authenticated');

-- 8. Create RLS policies for classes
DROP POLICY IF EXISTS "Allow read access to classes" ON classes;
CREATE POLICY "Allow read access to classes" ON classes
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow authenticated users to manage classes" ON classes;
CREATE POLICY "Allow authenticated users to manage classes" ON classes
  FOR ALL USING (auth.role() = 'authenticated');

-- 9. Create RLS policies for sections
DROP POLICY IF EXISTS "Allow read access to sections" ON sections;
CREATE POLICY "Allow read access to sections" ON sections
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow authenticated users to manage sections" ON sections;
CREATE POLICY "Allow authenticated users to manage sections" ON sections
  FOR ALL USING (auth.role() = 'authenticated');

-- 10. Create RLS policies for academic_years
DROP POLICY IF EXISTS "Allow read access to academic_years" ON academic_years;
CREATE POLICY "Allow read access to academic_years" ON academic_years
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow authenticated users to manage academic_years" ON academic_years;
CREATE POLICY "Allow authenticated users to manage academic_years" ON academic_years
  FOR ALL USING (auth.role() = 'authenticated');

-- 11. Create RLS policies for students
DROP POLICY IF EXISTS "Allow read access to students" ON students;
CREATE POLICY "Allow read access to students" ON students
  FOR SELECT USING (true);

DROP POLICY IF EXISTS "Allow authenticated users to manage students" ON students;
CREATE POLICY "Allow authenticated users to manage students" ON students
  FOR ALL USING (auth.role() = 'authenticated');

-- 12. Insert sample guardian relations data
INSERT INTO guardian_relations (name) VALUES 
  ('Father'),
  ('Mother'),
  ('Guardian'),
  ('Grandfather'),
  ('Grandmother'),
  ('Uncle'),
  ('Aunt'),
  ('Brother'),
  ('Sister'),
  ('Other')
ON CONFLICT DO NOTHING;

-- 13. Insert sample classes data
INSERT INTO classes (name, description, grade_level) VALUES 
  ('Nursery', 'Pre-school nursery class', 0),
  ('LKG', 'Lower Kindergarten', 1),
  ('UKG', 'Upper Kindergarten', 2),
  ('Class 1', 'First standard', 3),
  ('Class 2', 'Second standard', 4),
  ('Class 3', 'Third standard', 5),
  ('Class 4', 'Fourth standard', 6),
  ('Class 5', 'Fifth standard', 7),
  ('Class 6', 'Sixth standard', 8),
  ('Class 7', 'Seventh standard', 9),
  ('Class 8', 'Eighth standard', 10),
  ('Class 9', 'Ninth standard', 11),
  ('Class 10', 'Tenth standard', 12)
ON CONFLICT DO NOTHING;

-- 14. Insert sample sections data
INSERT INTO sections (name, max_capacity) VALUES 
  ('A', 30),
  ('B', 30),
  ('C', 30),
  ('D', 25)
ON CONFLICT DO NOTHING;

-- 15. Insert sample academic years data
INSERT INTO academic_years (year, start_date, end_date, is_active) VALUES 
  ('2023-24', '2023-04-01', '2024-03-31', false),
  ('2024-25', '2024-04-01', '2025-03-31', true),
  ('2025-26', '2025-04-01', '2026-03-31', false)
ON CONFLICT DO NOTHING;

-- 16. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_guardian_relations_active ON guardian_relations(is_active);
CREATE INDEX IF NOT EXISTS idx_classes_active ON classes(is_active);
CREATE INDEX IF NOT EXISTS idx_sections_active ON sections(is_active);
CREATE INDEX IF NOT EXISTS idx_academic_years_active ON academic_years(is_active);
CREATE INDEX IF NOT EXISTS idx_students_active ON students(is_active);
CREATE INDEX IF NOT EXISTS idx_students_class_section ON students(class_id, section_id);
CREATE INDEX IF NOT EXISTS idx_students_academic_year ON students(academic_year_id);

-- 17. Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 18. Create triggers for updated_at
DROP TRIGGER IF EXISTS update_guardian_relations_updated_at ON guardian_relations;
CREATE TRIGGER update_guardian_relations_updated_at 
    BEFORE UPDATE ON guardian_relations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_classes_updated_at ON classes;
CREATE TRIGGER update_classes_updated_at 
    BEFORE UPDATE ON classes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_sections_updated_at ON sections;
CREATE TRIGGER update_sections_updated_at 
    BEFORE UPDATE ON sections 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_academic_years_updated_at ON academic_years;
CREATE TRIGGER update_academic_years_updated_at 
    BEFORE UPDATE ON academic_years 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_students_updated_at ON students;
CREATE TRIGGER update_students_updated_at 
    BEFORE UPDATE ON students 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 19. Verify the setup
SELECT 'Guardian Relations table created successfully' as status;
SELECT 'Classes table created successfully' as status;
SELECT 'Sections table created successfully' as status;
SELECT 'Academic Years table created successfully' as status;
SELECT 'Students table created successfully' as status;
SELECT 'RLS policies created successfully' as status;
SELECT 'Sample data inserted successfully' as status;

-- 20. Check data counts
SELECT 
  'guardian_relations' as table_name,
  COUNT(*) as record_count
FROM guardian_relations
WHERE is_active = true
UNION ALL
SELECT 
  'classes' as table_name,
  COUNT(*) as record_count
FROM classes
WHERE is_active = true
UNION ALL
SELECT 
  'sections' as table_name,
  COUNT(*) as record_count
FROM sections
WHERE is_active = true
UNION ALL
SELECT 
  'academic_years' as table_name,
  COUNT(*) as record_count
FROM academic_years
WHERE is_active = true;

-- Final message
SELECT '🎉 Master data tables setup complete! Guardian relations dropdown should now work properly.' as message;
