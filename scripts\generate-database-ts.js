#!/usr/bin/env node

/**
 * Generate updated database.ts file based on actual Supabase schema
 */

const fs = require('fs');
const path = require('path');

// Load the schema data
const schemaPath = path.join(__dirname, '..', 'full-schema.json');
const schemaData = JSON.parse(fs.readFileSync(schemaPath, 'utf8'));

function generateDatabaseConstants() {
  const tables = Object.keys(schemaData.tables);
  
  // Generate DATABASE_TABLES constant
  let tablesConstant = 'export const DATABASE_TABLES = {\n';
  
  // Group tables by category
  const groups = schemaData.tableGroups;
  
  // Core tables
  tablesConstant += '  // Core tables\n';
  groups.core.forEach(table => {
    tablesConstant += `  ${table.toUpperCase()}: '${table}',\n`;
  });
  
  // Master data tables
  tablesConstant += '\n  // Master data tables\n';
  groups.master_data.forEach(table => {
    tablesConstant += `  ${table.toUpperCase()}: '${table}',\n`;
  });
  
  // Auth tables
  tablesConstant += '\n  // Authentication tables\n';
  groups.auth.forEach(table => {
    tablesConstant += `  ${table.toUpperCase()}: '${table}',\n`;
  });
  
  // Academic tables
  tablesConstant += '\n  // Academic management tables\n';
  groups.academic.forEach(table => {
    tablesConstant += `  ${table.toUpperCase()}: '${table}',\n`;
  });
  
  // Tracking tables
  tablesConstant += '\n  // Tracking tables\n';
  groups.tracking.forEach(table => {
    tablesConstant += `  ${table.toUpperCase()}: '${table}',\n`;
  });
  
  tablesConstant += '} as const;\n\n';
  
  return tablesConstant;
}

function generateColumnConstants() {
  let columnsConstant = 'export const DATABASE_COLUMNS = {\n';
  
  // Common columns first
  columnsConstant += '  // Common columns across tables\n';
  columnsConstant += '  COMMON: {\n';
  columnsConstant += '    ID: \'id\',\n';
  columnsConstant += '    CREATED_AT: \'created_at\',\n';
  columnsConstant += '    UPDATED_AT: \'updated_at\',\n';
  columnsConstant += '    IS_ACTIVE: \'is_active\'\n';
  columnsConstant += '  },\n\n';
  
  // Generate columns for each table
  Object.entries(schemaData.tables).forEach(([tableName, tableInfo]) => {
    columnsConstant += `  // ${tableName} table columns\n`;
    columnsConstant += `  ${tableName.toUpperCase()}: {\n`;
    
    tableInfo.columns.forEach(column => {
      const columnKey = column.name.toUpperCase();
      columnsConstant += `    ${columnKey}: '${column.name}',\n`;
    });
    
    columnsConstant += '  },\n\n';
  });
  
  columnsConstant += '} as const;\n\n';
  
  return columnsConstant;
}

function generateTypeDefinitions() {
  let typeDefs = '/**\n * TypeScript type definitions based on actual database schema\n */\n\n';
  
  // Generate entity interfaces
  Object.entries(schemaData.tables).forEach(([tableName, tableInfo]) => {
    const interfaceName = tableName.charAt(0).toUpperCase() + tableName.slice(1).replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()) + 'Entity';
    
    typeDefs += `export interface ${interfaceName} {\n`;
    
    tableInfo.columns.forEach(column => {
      const optional = column.nullable ? '?' : '';
      let tsType = 'string'; // default
      
      switch (column.type) {
        case 'uuid':
        case 'text':
        case 'date':
        case 'timestamp':
          tsType = 'string';
          break;
        case 'integer':
        case 'numeric':
          tsType = 'number';
          break;
        case 'boolean':
          tsType = 'boolean';
          break;
        default:
          tsType = 'string';
      }
      
      typeDefs += `  ${column.name}${optional}: ${tsType};\n`;
    });
    
    typeDefs += '}\n\n';
  });
  
  return typeDefs;
}

function generateValidationRules() {
  let validation = '/**\n * Validation rules based on actual schema\n */\nexport const VALIDATION_RULES = {\n';
  
  // Add validation for key tables
  const keyTables = ['students', 'guardians', 'classes', 'sections'];
  
  keyTables.forEach(tableName => {
    if (schemaData.tables[tableName]) {
      validation += `  ${tableName.toUpperCase()}: {\n`;
      
      schemaData.tables[tableName].columns.forEach(column => {
        if (!column.nullable && column.name !== 'id' && !column.name.includes('_at')) {
          validation += `    ${column.name.toUpperCase()}: { required: true },\n`;
        }
      });
      
      validation += '  },\n';
    }
  });
  
  validation += '} as const;\n\n';
  
  return validation;
}

function generateFullDatabaseFile() {
  const header = `// src/constants/database.ts
/**
 * Centralized database schema definitions and constants
 * Generated from actual Supabase database schema
 * Single source of truth for all table names, column names, and schema definitions
 */

`;

  const tablesConstant = generateDatabaseConstants();
  const columnsConstant = generateColumnConstants();
  const typeDefs = generateTypeDefinitions();
  const validationRules = generateValidationRules();
  
  const config = `/**
 * Database configuration constants
 */
export const DATABASE_CONFIG = {
  // Schema settings
  SCHEMA: 'public',
  
  // Pagination settings
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Connection settings
  CONNECTION_TIMEOUT: 30000, // 30 seconds
  QUERY_TIMEOUT: 60000, // 60 seconds
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // Session settings
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  
  // File upload settings
  STORAGE_BUCKET: 'student-documents',
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ],
  
  // Status enums
  DOCUMENT_STATUS: {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected'
  } as const,
  
  GENDER_OPTIONS: {
    MALE: 'male',
    FEMALE: 'female',
    OTHER: 'other'
  } as const
} as const;

`;

  const helpers = `/**
 * Helper functions for schema management
 */
export const getTableName = (tableKey: keyof typeof DATABASE_TABLES): string => {
  return DATABASE_TABLES[tableKey];
};

export const getColumnName = (table: keyof typeof DATABASE_COLUMNS, column: string): string => {
  const tableColumns = DATABASE_COLUMNS[table];
  if (!tableColumns || !(column in tableColumns)) {
    throw new Error(\`Column \${column} not found in table \${table}\`);
  }
  return (tableColumns as any)[column];
};

// Type exports for external use
export type DatabaseTableName = typeof DATABASE_TABLES[keyof typeof DATABASE_TABLES];
export type DatabaseColumnName = string;

// Schema version for migration tracking
export const SCHEMA_VERSION = '2.0.0';
export const LAST_UPDATED = '${new Date().toISOString()}';
`;

  return header + tablesConstant + columnsConstant + typeDefs + validationRules + config + helpers;
}

// Generate and save the file
const newDatabaseContent = generateFullDatabaseFile();
const outputPath = path.join(__dirname, '..', 'src', 'constants', 'database-new.ts');

fs.writeFileSync(outputPath, newDatabaseContent);

console.log('✅ Generated new database.ts file at src/constants/database-new.ts');
console.log('📊 Schema summary:');
console.log(`  - Tables: ${Object.keys(schemaData.tables).length}`);
console.log(`  - Total columns: ${Object.values(schemaData.tables).reduce((sum, table) => sum + table.columns.length, 0)}`);
console.log('\n🔍 Table groups:');
Object.entries(schemaData.tableGroups).forEach(([group, tables]) => {
  console.log(`  - ${group}: ${tables.join(', ')}`);
});

console.log('\n🎉 Database constants generation completed!');
console.log('📝 Next steps:');
console.log('  1. Review the generated file: src/constants/database-new.ts');
console.log('  2. Compare with existing database.ts');
console.log('  3. Replace the old file when ready');
