# ===============================================================================
#                           EDUPRO NEXT.JS PROJECT - .GITIGNORE
# ===============================================================================
#
# This .gitignore file is optimized for Next.js projects with comprehensive
# coverage of development artifacts, sensitive files, and build outputs.
#
# SECURITY NOTE: Environment files containing sensitive data are ignored.
# Use .env.example as a template for required environment variables.
#
# ===============================================================================

# ===== DEPENDENCIES =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.pnpm-debug.log*

# Package manager lock files (keep package-lock.json for npm projects)
# yarn.lock
# pnpm-lock.yaml

# ===== NEXT.JS BUILD OUTPUTS =====
.next/
out/
build/
dist/
.vercel/
.turbo/

# Next.js cache and build info
.next/cache/
.next/static/
.next/server/
.next/standalone/

# ===== ENVIRONMENT VARIABLES (CRITICAL SECURITY) =====
# All environment files are ignored to prevent accidental commit of secrets
.env
.env.local
.env.development
.env.development.local
.env.test
.env.test.local
.env.production
.env.production.local
.env.staging
.env.staging.local

# ===== TYPESCRIPT =====
*.tsbuildinfo
next-env.d.ts
*.d.ts.map

# ===== OPERATING SYSTEM FILES =====
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
*~
.fuse_hidden*
.directory
.Trash-*

# ===== IDEs AND EDITORS =====
# Visual Studio Code
.vscode/
!.vscode/extensions.json
!.vscode/settings.json.example

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ===== LOGS AND RUNTIME DATA =====
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# ===== TESTING AND COVERAGE =====
coverage/
*.lcov
.nyc_output
.jest-cache/
test-results/
playwright-report/
playwright/.cache/
.coverage/

# ===== CACHE DIRECTORIES =====
.npm
.eslintcache
.stylelintcache
.cache/
.parcel-cache/

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===== STORYBOOK =====
.out
.storybook-out
storybook-static/

# ===== TEMPORARY FILES =====
tmp/
temp/
*.tmp
*.temp

# ===== DEPLOYMENT AND HOSTING =====
.vercel
.netlify/
.firebase/
.surge/

# ===== DATABASE =====
*.db
*.sqlite
*.sqlite3

# ===== SECURITY AND SECRETS =====
# API keys and secrets
secrets/
.secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# ===== DEVELOPMENT AND DEBUG FILES =====
# Debug files
debug.log
*.debug
.debug/

# Test files (uncomment if you want to ignore test files)
# test-*.js
# test-*.ts
# *.test.js
# *.test.ts
# *.spec.js
# *.spec.ts

# ===== BUNDLE ANALYZER =====
.bundle-analyzer/
bundle-analyzer-report.html

# ===== MISCELLANEOUS =====
# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# ===== PROJECT-SPECIFIC =====
# Add any project-specific files or directories here
# uploads/
# user-uploads/

# ===== ADDITIONAL DEVELOPMENT FILES =====
# Duplicate files
*.duplicate
*.copy
*-copy.*
*-backup.*
*-old.*

# Debug and test files
debug/
*.debug.js
*.debug.ts
test-*.js
test-*.ts
*-test.*
*-spec.*

# Temporary development files
scratch/
playground/
experiments/
dev-notes/
todo.md
notes.md

# IDE and editor specific files
*.swp
*.swo
.vscode/settings.json
.vscode/launch.json
.idea/workspace.xml
.idea/tasks.xml

# OS generated files
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# Package manager files (optional - uncomment if needed)
# .pnpm-store/
# .yarn/
# .npm/

# Build artifacts
*.tgz
*.tar.gz
*.zip
*.rar

# Documentation drafts
*-draft.*
*-wip.*
