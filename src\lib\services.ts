// src/lib/services.ts
/**
 * Server-side service initialization for Next.js App Router
 * This file provides server-side service instances that are compatible with SSR/SSG
 */

import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { Database } from '../types/database';

// Service imports
import { DocumentManagementService } from '../services/documentManagementService';
import { ValidationService } from '../services/enrollment/validationService';
import { EnrollmentManagementService } from '../services/enrollmentManagementService';
import { StudentManagementService } from '../services/studentManagementService';

/**
 * Server-side service container
 */
export interface ServerServiceContainer {
  studentManagementService: StudentManagementService;
  enrollmentManagementService: EnrollmentManagementService;
  documentManagementService: DocumentManagementService;
  validationService: ValidationService;
}

/**
 * Create server-side service container
 * This function creates a new service container for each request
 * ensuring proper isolation and SSR compatibility
 */
export async function createServerServices(): Promise<ServerServiceContainer> {
  // Create Supabase client for server components
  const cookieStore = await cookies();
  const supabase = createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet: Array<{ name: string; value: string; options?: any }>) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );

  // Initialize services with server-side client
  const studentManagementService = new StudentManagementService(supabase);
  const enrollmentManagementService = new EnrollmentManagementService(supabase);
  const documentManagementService = new DocumentManagementService(supabase);
  const validationService = new ValidationService(supabase);

  return {
    studentManagementService,
    enrollmentManagementService,
    documentManagementService,
    validationService,
  };
}

/**
 * Get student management service for server components
 */
export async function getStudentManagementService(): Promise<StudentManagementService> {
  const services = await createServerServices();
  return services.studentManagementService;
}

/**
 * Get enrollment management service for server components
 */
export async function getEnrollmentManagementService(): Promise<EnrollmentManagementService> {
  const services = await createServerServices();
  return services.enrollmentManagementService;
}

/**
 * Get document management service for server components
 */
export async function getDocumentManagementService(): Promise<DocumentManagementService> {
  const services = await createServerServices();
  return services.documentManagementService;
}

/**
 * Get validation service for server components
 */
export async function getValidationService(): Promise<ValidationService> {
  const services = await createServerServices();
  return services.validationService;
}

/**
 * Server action helper for student operations
 * Use this in server actions and route handlers
 */
export async function withStudentService<T>(
  operation: (service: StudentManagementService) => Promise<T>
): Promise<T> {
  const service = await getStudentManagementService();
  return await operation(service);
}

/**
 * Server action helper for enrollment operations
 * Use this in server actions and route handlers
 */
export async function withEnrollmentService<T>(
  operation: (service: EnrollmentManagementService) => Promise<T>
): Promise<T> {
  const service = await getEnrollmentManagementService();
  return await operation(service);
}

/**
 * Server action helper for document operations
 * Use this in server actions and route handlers
 */
export async function withDocumentService<T>(
  operation: (service: DocumentManagementService) => Promise<T>
): Promise<T> {
  const service = await getDocumentManagementService();
  return await operation(service);
}

/**
 * Error boundary for service operations
 * Provides consistent error handling across server operations
 */
export async function safeServiceOperation<T>(
  operation: () => Promise<T>,
  fallback?: T
): Promise<{ data: T | null; error: string | null }> {
  try {
    const data = await operation();
    return { data, error: null };
  } catch (error) {
    console.error('Service operation failed:', error);
    return {
      data: fallback || null,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Batch operation helper
 * Executes multiple service operations in parallel with error handling
 */
export async function batchServiceOperations<T>(
  operations: Array<() => Promise<T>>
): Promise<Array<{ data: T | null; error: string | null }>> {
  const results = await Promise.allSettled(
    operations.map(op => safeServiceOperation(op))
  );

  return results.map(result => {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      return {
        data: null,
        error: result.reason instanceof Error ? result.reason.message : 'Operation failed',
      };
    }
  });
}

/**
 * Service health check for monitoring
 */
export async function checkServiceHealth(): Promise<{
  status: 'healthy' | 'unhealthy';
  services: Record<string, { status: string; latency?: number; error?: string }>;
}> {
  const startTime = Date.now();
  const serviceStatus: Record<string, { status: string; latency?: number; error?: string }> = {};

  try {
    // Test database connection through student service
    const studentService = await getStudentManagementService();
    const testResult = await safeServiceOperation(async () => {
      // Simple operation to test connectivity
      return await studentService.searchStudents({ searchTerm: '' }, { page: 1, pageSize: 1 });
    });

    const latency = Date.now() - startTime;

    if (testResult.error) {
      serviceStatus.database = {
        status: 'unhealthy',
        error: testResult.error,
        latency,
      };
    } else {
      serviceStatus.database = {
        status: 'healthy',
        latency,
      };
    }

    // Mark other services as healthy if database is healthy
    const dependentServices = ['studentManagement', 'enrollmentManagement', 'documentManagement'];
    for (const service of dependentServices) {
      serviceStatus[service] = {
        status: serviceStatus.database.status,
      };
    }

    const overallStatus = serviceStatus.database.status === 'healthy' ? 'healthy' : 'unhealthy';

    return {
      status: overallStatus,
      services: serviceStatus,
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      services: {
        system: {
          status: 'unhealthy',
          error: error instanceof Error ? error.message : 'System error',
        },
      },
    };
  }
}

/**
 * Service metrics collection
 */
export interface ServiceMetrics {
  operationCount: number;
  averageLatency: number;
  errorRate: number;
  lastOperation: Date;
}

// Simple in-memory metrics store (in production, use Redis or similar)
const metricsStore = new Map<string, ServiceMetrics>();

/**
 * Record service operation metrics
 */
export function recordServiceMetrics(
  serviceName: string,
  latency: number,
  success: boolean
): void {
  const existing = metricsStore.get(serviceName) || {
    operationCount: 0,
    averageLatency: 0,
    errorRate: 0,
    lastOperation: new Date(),
  };

  const newCount = existing.operationCount + 1;
  const newAverageLatency = (existing.averageLatency * existing.operationCount + latency) / newCount;
  const newErrorRate = success
    ? (existing.errorRate * existing.operationCount) / newCount
    : (existing.errorRate * existing.operationCount + 1) / newCount;

  metricsStore.set(serviceName, {
    operationCount: newCount,
    averageLatency: newAverageLatency,
    errorRate: newErrorRate,
    lastOperation: new Date(),
  });
}

/**
 * Get service metrics
 */
export function getServiceMetrics(serviceName?: string): Record<string, ServiceMetrics> {
  if (serviceName) {
    const metrics = metricsStore.get(serviceName);
    return metrics ? { [serviceName]: metrics } : {};
  }

  return Object.fromEntries(metricsStore.entries());
}

/**
 * Clear service metrics
 */
export function clearServiceMetrics(serviceName?: string): void {
  if (serviceName) {
    metricsStore.delete(serviceName);
  } else {
    metricsStore.clear();
  }
}
