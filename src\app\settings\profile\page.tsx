// src/app/settings/profile/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { AppLayout } from '../../../components/layout';
import { getAuthState } from '../../../lib/auth';

interface ProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address: string;
  bio: string;
  portfolio: string;
  linkedin: string;
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface User {
  name: string;
  email: string;
  role: string;
  isAuthenticated: boolean;
}

export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('Account');
  const [user, setUser] = useState<User>({ name: '', email: '', role: '', isAuthenticated: false });
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    watch,
  } = useForm<ProfileFormData>({
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      address: '',
      bio: '',
      portfolio: '',
      linkedin: '',
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  useEffect(() => {
    const loadAuthState = async () => {
      try {
        setIsLoading(true);
        const authState = await getAuthState();
        if (authState) {
          setUser(authState);
          
          // Parse name into first and last name
          const nameParts = authState.name.split(' ');
          const firstName = nameParts[0] || '';
          const lastName = nameParts.slice(1).join(' ') || '';
          
          // Set default form values
          reset({
            firstName,
            lastName,
            email: authState.email,
            phoneNumber: '+****************',
            address: '123 Blossom Lane, Apt 4B, San Francisco, CA 94107',
            bio: 'Passionate product designer with a knack for creating intuitive and engaging user experiences. Always eager to learn and collaborate on innovative projects.',
            portfolio: 'portfolio.example.com',
            linkedin: 'linkedin.com/in/sophiab',
            currentPassword: '',
            newPassword: '',
            confirmPassword: '',
          });
        } else {
          // Handle case where user is not authenticated
          setUser({ name: 'Guest User', email: '<EMAIL>', role: 'guest', isAuthenticated: false });
        }
      } catch (error) {
        console.error('Error loading auth state:', error);
        setUser({ name: 'Guest User', email: '<EMAIL>', role: 'guest', isAuthenticated: false });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadAuthState();
  }, [reset]);

  const onSubmit = (data: ProfileFormData) => {
    console.log('Profile updated:', data);
    
    // Update localStorage with new data
    const fullName = `${data.firstName} ${data.lastName}`.trim();
    localStorage.setItem('userName', fullName);
    localStorage.setItem('userEmail', data.email);
    
    // Update local state
    setUser(prev => ({
      ...prev,
      name: fullName,
      email: data.email,
    }));
    
    setIsEditing(false);
    alert('Profile updated successfully!');
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
  };

  const tabs = [
    { id: 'Account', name: 'Account', icon: '👤' },
    { id: 'Actions', name: 'Actions', icon: '⚡' },
    { id: 'Settings', name: 'Settings', icon: '⚙️' },
  ];

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const formatRole = (role: string) => {
    return role.charAt(0).toUpperCase() + role.slice(1);
  };

  return (
    <AppLayout title="Profile Settings">
      {isLoading ? (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading profile...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Main Content */}
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Sidebar - Profile Card */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
              {/* Profile Header */}
              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 px-5 py-6 text-center relative">
                <div className="relative inline-block">
                  <div className="w-20 h-20 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                    <span className="text-white text-xl font-bold">
                      {getInitials(user.name)}
                    </span>
                  </div>
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center border-4 border-white">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                
                <h2 className="text-lg font-bold text-gray-900 mb-1">{user.name || 'User Name'}</h2>
                <p className="text-gray-600 text-sm mb-3">{formatRole(user.role)}</p>
                <div className="flex items-center justify-center text-sm text-gray-500 mb-4">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  San Francisco, CA
                </div>
                
                <div className="space-y-3">
                  <button
                    onClick={() => setIsEditing(true)}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors text-sm"
                  >
                    Edit Profile
                  </button>
                  
                  <button className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors text-sm">
                    View Public Profile
                  </button>
                </div>
              </div>

              {/* About Me Section */}
              <div className="p-5">
                <h3 className="text-base font-semibold text-gray-900 mb-3">About Me</h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-5">
                  {watch('bio') || 'Passionate product designer with a knack for creating intuitive and engaging user experiences. Always eager to learn and collaborate on innovative projects.'}
                </p>
                
                <h4 className="text-sm font-semibold text-gray-900 mb-3">Contact</h4>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <svg className="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9" />
                    </svg>
                    {watch('portfolio') || 'portfolio.example.com'}
                  </div>
                  
                  <div className="flex items-center text-sm text-gray-600">
                    <svg className="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                    </svg>
                    {watch('linkedin') || 'linkedin.com/in/sophiab'}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Content - Account Information */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200">
              {/* Tab Navigation */}
              <div className="border-b border-gray-200">
                <nav className="flex space-x-8 px-6">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <span className="mr-2">{tab.icon}</span>
                      {tab.name}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              <div className="p-6">
                {activeTab === 'Account' && (
                  <div>
                    <div className="mb-6">
                      <h2 className="text-xl font-semibold text-gray-900 mb-2">Account Information</h2>
                      <p className="text-gray-600 text-sm">Update your account details and personal information.</p>
                    </div>

                    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                      {/* Personal Information */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            First Name
                          </label>
                          <input
                            type="text"
                            {...register('firstName', { required: 'First name is required' })}
                            disabled={!isEditing}
                            className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                              !isEditing ? 'bg-gray-50 text-gray-500' : 'bg-white'
                            }`}
                          />
                          {errors.firstName && (
                            <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                          )}
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Last Name
                          </label>
                          <input
                            type="text"
                            {...register('lastName', { required: 'Last name is required' })}
                            disabled={!isEditing}
                            className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                              !isEditing ? 'bg-gray-50 text-gray-500' : 'bg-white'
                            }`}
                          />
                          {errors.lastName && (
                            <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
                          )}
                        </div>
                      </div>

                      {/* Contact Information */}
                      <div className="space-y-6">
                        <div className="bg-blue-50 rounded-lg p-4 flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                              </svg>
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="text-sm font-medium text-gray-900">Email Address</h4>
                                <p className="text-sm text-gray-600 mt-1">{watch('email')}</p>
                              </div>
                              <button
                                type="button"
                                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                              >
                                Change
                              </button>
                            </div>
                          </div>
                        </div>

                        <div className="bg-blue-50 rounded-lg p-4 flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                              </svg>
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="text-sm font-medium text-gray-900">Phone Number</h4>
                                <p className="text-sm text-gray-600 mt-1">{watch('phoneNumber')}</p>
                              </div>
                              <button
                                type="button"
                                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                              >
                                Change
                              </button>
                            </div>
                          </div>
                        </div>

                        <div className="bg-blue-50 rounded-lg p-4 flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                              </svg>
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="text-sm font-medium text-gray-900">Address</h4>
                                <p className="text-sm text-gray-600 mt-1">{watch('address')}</p>
                              </div>
                              <button
                                type="button"
                                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                              >
                                Edit
                              </button>
                            </div>
                          </div>
                        </div>

                        <div className="bg-blue-50 rounded-lg p-4 flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                              </svg>
                            </div>
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="text-sm font-medium text-gray-900">Password</h4>
                                <p className="text-sm text-gray-600 mt-1">Last changed on July 15, 2023</p>
                              </div>
                              <button
                                type="button"
                                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                              >
                                Change Password
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      {isEditing && (
                        <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                          <button
                            type="button"
                            onClick={handleCancel}
                            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Cancel
                          </button>
                          <button
                            type="submit"
                            disabled={!isDirty}
                            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Save Changes
                          </button>
                        </div>
                      )}
                    </form>
                  </div>
                )}

                {activeTab === 'Actions' && (
                  <div>
                    <div className="mb-6">
                      <h2 className="text-xl font-semibold text-gray-900 mb-2">Account Actions</h2>
                      <p className="text-gray-600 text-sm">Manage your account settings and preferences.</p>
                    </div>

                    <div className="space-y-4">
                      <div className="p-4 border border-gray-200 rounded-lg">
                        <h3 className="font-medium text-gray-900 mb-2">Export Data</h3>
                        <p className="text-sm text-gray-600 mb-3">Download a copy of your account data.</p>
                        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                          Request Export
                        </button>
                      </div>

                      <div className="p-4 border border-gray-200 rounded-lg">
                        <h3 className="font-medium text-gray-900 mb-2">Delete Account</h3>
                        <p className="text-sm text-gray-600 mb-3">Permanently delete your account and all data.</p>
                        <button className="text-red-600 hover:text-red-700 text-sm font-medium">
                          Delete Account
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'Settings' && (
                  <div>
                    <div className="mb-6">
                      <h2 className="text-xl font-semibold text-gray-900 mb-2">Account Settings</h2>
                      <p className="text-gray-600 text-sm">Configure your account preferences and privacy settings.</p>
                    </div>

                    <div className="space-y-6">
                      <div>
                        <h3 className="font-medium text-gray-900 mb-3">Notifications</h3>
                        <div className="space-y-3">
                          <label className="flex items-center">
                            <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" defaultChecked />
                            <span className="ml-2 text-sm text-gray-700">Email notifications</span>
                          </label>
                          <label className="flex items-center">
                            <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                            <span className="ml-2 text-sm text-gray-700">SMS notifications</span>
                          </label>
                        </div>
                      </div>

                      <div>
                        <h3 className="font-medium text-gray-900 mb-3">Privacy</h3>
                        <div className="space-y-3">
                          <label className="flex items-center">
                            <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" defaultChecked />
                            <span className="ml-2 text-sm text-gray-700">Make profile public</span>
                          </label>
                          <label className="flex items-center">
                            <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" defaultChecked />
                            <span className="ml-2 text-sm text-gray-700">Allow search engines to index profile</span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
          </div>
        </>
      )}
    </AppLayout>
  );
}
