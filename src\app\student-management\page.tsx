// src/app/student-management/page.tsx
'use client';

import { useState } from 'react';
import { AppLayout } from '../../components/layout';
import StudentManagement from './_components/student-management';
import './student-management.css';

export default function StudentManagementPage() {
  const [activeSubSection, setActiveSubSection] = useState('Current Students');

  return (
    <AppLayout title="Student Management">
      <StudentManagement
        activeSubSection={activeSubSection}
        onSubSectionChange={setActiveSubSection}
      />
    </AppLayout>
  );
}
