// src/app/staff-management/page.tsx
'use client';

import { useState } from 'react';

export default function StaffManagementPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl-app font-bold text-slate-900">Staff Management</h1>
          <p className="text-base-app text-slate-600 mt-1">
            Manage teachers, administrative staff, and personnel records
          </p>
        </div>
        <button className="btn-primary">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Staff Member
        </button>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-slate-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: '📊' },
            { id: 'teachers', label: 'Teachers', icon: '👨‍🏫' },
            { id: 'admin', label: 'Admin Staff', icon: '👥' },
            { id: 'departments', label: 'Departments', icon: '🏢' },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm-app transition-colors ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Content Area */}
      <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <h2 className="text-xl-app font-semibold text-slate-900">Staff Overview</h2>
            
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { title: 'Total Staff', value: '45', change: '+3', color: 'blue' },
                { title: 'Teachers', value: '32', change: '+2', color: 'green' },
                { title: 'Admin Staff', value: '13', change: '+1', color: 'purple' },
                { title: 'Departments', value: '8', change: '0', color: 'orange' },
              ].map((stat, index) => (
                <div key={index} className="bg-slate-50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm-app font-medium text-slate-600">{stat.title}</p>
                      <p className="text-2xl-app font-bold text-slate-900">{stat.value}</p>
                    </div>
                    <div className={`text-sm-app font-medium ${
                      stat.change.startsWith('+') ? 'text-green-600' : 
                      stat.change === '0' ? 'text-slate-500' : 'text-red-600'
                    }`}>
                      {stat.change !== '0' && stat.change}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Recent Activity */}
            <div>
              <h3 className="text-lg-app font-medium text-slate-900 mb-4">Recent Activity</h3>
              <div className="space-y-3">
                {[
                  'New teacher John Smith added to Mathematics department',
                  'Sarah Johnson updated contact information',
                  'Physics department meeting scheduled for tomorrow',
                ].map((activity, index) => (
                  <div key={index} className="flex items-center space-x-3 text-sm-app text-slate-600">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>{activity}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'teachers' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">👨‍🏫</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Teachers Management</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Manage teacher profiles, qualifications, and assignments
            </p>
            <button className="btn-primary">Add New Teacher</button>
          </div>
        )}

        {activeTab === 'admin' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">👥</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Administrative Staff</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Manage administrative personnel and support staff
            </p>
            <button className="btn-primary">Add Admin Staff</button>
          </div>
        )}

        {activeTab === 'departments' && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🏢</div>
            <h3 className="text-lg-app font-medium text-slate-900 mb-2">Departments</h3>
            <p className="text-base-app text-slate-600 mb-6">
              Organize staff into departments and manage hierarchies
            </p>
            <button className="btn-primary">Create Department</button>
          </div>
        )}
      </div>
    </div>
  );
}
