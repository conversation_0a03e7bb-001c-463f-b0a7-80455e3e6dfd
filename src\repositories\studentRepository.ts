// src/repositories/studentRepository.ts
import { SupabaseClient } from '@supabase/supabase-js';
import {
  DATABASE_COLUMNS,
  DATABASE_TABLES,
  StudentEntity,
  StudentInsert,
  StudentUpdate
} from '../constants/database';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorHandler, ServiceError } from '../services/core/errorHandler';
import { Database } from '../types/database';
import { BaseRepository, PaginatedResult, PaginationOptions } from './baseRepository';

/**
 * Student-specific repository interface
 */
export interface IStudentRepository {
  // Basic CRUD operations
  create(data: StudentInsert): Promise<StudentEntity>;
  findById(id: string): Promise<StudentEntity | null>;
  findAll(filters?: Record<string, any>, pagination?: PaginationOptions): Promise<PaginatedResult<StudentEntity>>;
  update(id: string, data: StudentUpdate): Promise<StudentEntity>;
  softDelete(id: string): Promise<void>;

  // Student-specific methods
  findByEmail(email: string): Promise<StudentEntity | null>;
  findByRollNumber(rollNumber: string, classId: string, sectionId: string, academicYearId: string): Promise<StudentEntity | null>;
  findByClassAndSection(classId: string, sectionId: string, academicYearId: string, pagination?: PaginationOptions): Promise<PaginatedResult<StudentEntity>>;
  findByGuardianPhone(phone: string): Promise<StudentEntity[]>;
  isEmailUnique(email: string, excludeId?: string): Promise<boolean>;
  isRollNumberUnique(rollNumber: string, classId: string, sectionId: string, academicYearId: string, excludeId?: string): Promise<boolean>;
  searchStudents(searchTerm: string, pagination?: PaginationOptions): Promise<PaginatedResult<StudentEntity>>;
  getStudentsByAcademicYear(academicYearId: string, pagination?: PaginationOptions): Promise<PaginatedResult<StudentEntity>>;
}

/**
 * Student repository implementation
 * Handles all database operations related to students
 */
export class StudentRepository extends BaseRepository<StudentEntity, StudentInsert, StudentUpdate> implements IStudentRepository {
  
  constructor(client: SupabaseClient<Database>) {
    super(client, DATABASE_TABLES.STUDENTS);
  }

  /**
   * Find student by email address
   * @param email - Student email address
   * @returns Student entity or null if not found
   */
  async findByEmail(email: string): Promise<StudentEntity | null> {
    try {
      this.log('info', 'Finding student by email', { email });

      const student = await this.findOne({
        [DATABASE_COLUMNS.STUDENTS.EMAIL]: email
      });

      this.log('info', 'Student found by email', { email, found: !!student });
      return student;
    } catch (error) {
      this.log('error', 'Error finding student by email', { error, email });
      throw ErrorHandler.handle(error, 'Find student by email');
    }
  }

  /**
   * Find student by roll number within a specific class, section, and academic year
   * @param rollNumber - Student roll number
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @returns Student entity or null if not found
   */
  async findByRollNumber(
    rollNumber: string,
    classId: string,
    sectionId: string,
    academicYearId: string
  ): Promise<StudentEntity | null> {
    try {
      this.log('info', 'Finding student by roll number', { rollNumber, classId, sectionId, academicYearId });

      const student = await this.findOne({
        [DATABASE_COLUMNS.STUDENTS.ROLL_NUMBER]: rollNumber,
        [DATABASE_COLUMNS.STUDENTS.CLASS_ID]: classId,
        [DATABASE_COLUMNS.STUDENTS.SECTION_ID]: sectionId,
        [DATABASE_COLUMNS.STUDENTS.ACADEMIC_YEAR_ID]: academicYearId
      });

      this.log('info', 'Student found by roll number', { rollNumber, found: !!student });
      return student;
    } catch (error) {
      this.log('error', 'Error finding student by roll number', { error, rollNumber });
      throw ErrorHandler.handle(error, 'Find student by roll number');
    }
  }

  /**
   * Find all students in a specific class and section
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @param pagination - Pagination options
   * @returns Paginated list of students
   */
  async findByClassAndSection(
    classId: string,
    sectionId: string,
    academicYearId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<StudentEntity>> {
    try {
      this.log('info', 'Finding students by class and section', { classId, sectionId, academicYearId });

      const filters = {
        [DATABASE_COLUMNS.STUDENTS.CLASS_ID]: classId,
        [DATABASE_COLUMNS.STUDENTS.SECTION_ID]: sectionId,
        [DATABASE_COLUMNS.STUDENTS.ACADEMIC_YEAR_ID]: academicYearId
      };

      const result = await this.findAll(filters, {
        ...pagination,
        sortBy: DATABASE_COLUMNS.STUDENTS.ROLL_NUMBER,
        sortOrder: 'asc'
      });

      this.log('info', 'Students found by class and section', { 
        classId, 
        sectionId, 
        academicYearId, 
        count: result.data.length 
      });

      return result;
    } catch (error) {
      this.log('error', 'Error finding students by class and section', { error, classId, sectionId });
      throw ErrorHandler.handle(error, 'Find students by class and section');
    }
  }

  /**
   * Find students by guardian phone number
   * @param phone - Guardian phone number
   * @returns Array of student entities
   */
  async findByGuardianPhone(phone: string): Promise<StudentEntity[]> {
    try {
          this.log('info', 'Finding students by guardian phone', { phone });

      const { data, error } = await this.client
        .from(this.tableName)
        .select('*')
        .eq(DATABASE_COLUMNS.STUDENTS.GUARDIAN_PHONE, phone)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (error) {
        throw ErrorHandler.handle(error, 'Find students by guardian phone');
      }

      this.log('info', 'Students found by guardian phone', { phone, count: data?.length || 0 });
      return (data || []) as StudentEntity[];
    } catch (error) {
      this.log('error', 'Error finding students by guardian phone', { error, phone });
      throw ErrorHandler.handle(error, 'Find students by guardian phone');
    }
  }

  /**
   * Check if email is unique
   * @param email - Email to check
   * @param excludeId - Student ID to exclude from check
   * @returns True if email is unique
   */
  async isEmailUnique(email: string, excludeId?: string): Promise<boolean> {
    try {
      this.log('info', 'Checking email uniqueness', { email, excludeId });

      let query = this.client
        .from(this.tableName)
        .select(DATABASE_COLUMNS.COMMON.ID)
        .eq(DATABASE_COLUMNS.STUDENTS.EMAIL, email)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (excludeId) {
        query = query.neq(DATABASE_COLUMNS.COMMON.ID, excludeId);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Check email uniqueness');
      }

      const isUnique = data === null;
      this.log('info', 'Email uniqueness checked', { email, isUnique });
      return isUnique;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return true;
      }
      this.log('error', 'Error checking email uniqueness', { error, email });
      throw error;
    }
  }

  /**
   * Check if roll number is unique within class, section, and academic year
   * @param rollNumber - Roll number to check
   * @param classId - Class ID
   * @param sectionId - Section ID
   * @param academicYearId - Academic year ID
   * @param excludeId - Student ID to exclude from check
   * @returns True if roll number is unique
   */
  async isRollNumberUnique(
    rollNumber: string,
    classId: string,
    sectionId: string,
    academicYearId: string,
    excludeId?: string
  ): Promise<boolean> {
    try {
      this.log('info', 'Checking roll number uniqueness', { 
        rollNumber, 
        classId, 
        sectionId, 
        academicYearId, 
              excludeId 
      });

      let query = this.client
        .from(this.tableName)
        .select(DATABASE_COLUMNS.COMMON.ID)
        .eq(DATABASE_COLUMNS.STUDENTS.ROLL_NUMBER, rollNumber)
        .eq(DATABASE_COLUMNS.STUDENTS.CLASS_ID, classId)
        .eq(DATABASE_COLUMNS.STUDENTS.SECTION_ID, sectionId)
        .eq(DATABASE_COLUMNS.STUDENTS.ACADEMIC_YEAR_ID, academicYearId)
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true);

      if (excludeId) {
        query = query.neq(DATABASE_COLUMNS.COMMON.ID, excludeId);
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') {
        throw ErrorHandler.handle(error, 'Check roll number uniqueness');
      }

      const isUnique = data === null;
      this.log('info', 'Roll number uniqueness checked', { rollNumber, isUnique });
      return isUnique;
    } catch (error) {
      if (error instanceof ServiceError && error.code === ErrorCode.NOT_FOUND) {
        return true;
      }
      this.log('error', 'Error checking roll number uniqueness', { error, rollNumber });
      throw error;
    }
  }

  /**
   * Search students by name, email, or roll number
   * @param searchTerm - Search term
   * @param pagination - Pagination options
   * @returns Paginated search results
   */
  async searchStudents(
    searchTerm: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<StudentEntity>> {
    try {
      this.log('info', 'Searching students', { searchTerm });

      const {
        page = 1,
        pageSize = 20,
                sortBy = DATABASE_COLUMNS.COMMON.CREATED_AT,
        sortOrder = 'desc'
      } = pagination || {};

      // Build search query
      const { data, error, count } = await this.client
        .from(this.tableName)
        .select('*', { count: 'exact' })
        .eq(DATABASE_COLUMNS.COMMON.IS_ACTIVE, true)
        .or(`${DATABASE_COLUMNS.STUDENTS.FIRST_NAME}.ilike.%${searchTerm}%,${DATABASE_COLUMNS.STUDENTS.LAST_NAME}.ilike.%${searchTerm}%,${DATABASE_COLUMNS.STUDENTS.EMAIL}.ilike.%${searchTerm}%,${DATABASE_COLUMNS.STUDENTS.ROLL_NUMBER}.ilike.%${searchTerm}%`)
        .order(sortBy, { ascending: sortOrder === 'asc' })
        .range((page - 1) * pageSize, page * pageSize - 1);

      if (error) {
        throw ErrorHandler.handle(error, 'Search students');
      }

      const totalCount = count || 0;
      const totalPages = Math.ceil(totalCount / pageSize);

      const result = {
        data: (data || []) as StudentEntity[],
        pagination: {
          page,
          pageSize,
          totalCount,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        }
      };

      this.log('info', 'Students search completed', { searchTerm, count: result.data.length });
      return result;
    } catch (error) {
      this.log('error', 'Error searching students', { error, searchTerm });
      throw ErrorHandler.handle(error, 'Search students');
    }
  }

  /**
   * Get students by academic year
   * @param academicYearId - Academic year ID
   * @param pagination - Pagination options
   * @returns Paginated list of students
   */
  async getStudentsByAcademicYear(
    academicYearId: string,
    pagination?: PaginationOptions
  ): Promise<PaginatedResult<StudentEntity>> {
    try {
      this.log('info', 'Getting students by academic year', { academicYearId });

      const filters = {
        [DATABASE_COLUMNS.STUDENTS.ACADEMIC_YEAR_ID]: academicYearId
      };

      const result = await this.findAll(filters, pagination);

      this.log('info', 'Students found by academic year', { 
        academicYearId, 
        count: result.data.length 
      });

      return result;
    } catch (error) {
      this.log('error', 'Error getting students by academic year', { error, academicYearId });
      throw ErrorHandler.handle(error, 'Get students by academic year');
    }
  }

  /**
   * Override create method to add student-specific validation
   */
  async create(data: StudentInsert): Promise<StudentEntity> {
    try {
      this.log('info', 'Creating student', { 
        firstName: data.first_name, 
        lastName: data.last_name 
      });

      // Validate required fields
      this.validateEntity(data as any, [
        'first_name',
        'last_name',
        'date_of_birth',
        'gender',
        'guardian_name',
        'guardian_relation_id',
        'guardian_phone',
        'class_id',
        'section_id',
        'roll_number',
        'academic_year_id'
      ] as any);

      // Validate email uniqueness if provided
      if (data.email) {
        const isEmailUnique = await this.isEmailUnique(data.email);
        if (!isEmailUnique) {
          throw new ServiceError(
            ErrorCode.DUPLICATE_VALUE,
            'Email address already exists',
            null,
            'Create student'
          );
        }
      }

      // Validate roll number uniqueness
      const isRollNumberUnique = await this.isRollNumberUnique(
        data.roll_number,
        data.class_id,
        data.section_id,
        data.academic_year_id
      );

      if (!isRollNumberUnique) {
        throw new ServiceError(
          ErrorCode.ROLL_NUMBER_TAKEN,
          'Roll number already exists in this class and section',
          null,
          'Create student'
        );
      }

      const student = await super.create(data);

      this.log('info', 'Student created successfully', { 
        studentId: student.id,
        rollNumber: student.roll_number 
      });

      return student;
    } catch (error) {
      this.log('error', 'Error creating student', { error, data });
      throw ErrorHandler.handle(error, 'Create student');
    }
  }

  /**
   * Override update method to add student-specific validation
   */
  async update(id: string, data: StudentUpdate): Promise<StudentEntity> {
    try {
      this.log('info', 'Updating student', { studentId: id });

      // Validate email uniqueness if being updated
      if (data.email) {
        const isEmailUnique = await this.isEmailUnique(data.email, id);
        if (!isEmailUnique) {
          throw new ServiceError(
            ErrorCode.DUPLICATE_VALUE,
            'Email address already exists',
            null,
            'Update student'
          );
        }
      }

      // Validate roll number uniqueness if being updated
      if (data.roll_number && data.class_id && data.section_id && data.academic_year_id) {
        const isRollNumberUnique = await this.isRollNumberUnique(
          data.roll_number,
          data.class_id,
          data.section_id,
          data.academic_year_id,
          id
        );

        if (!isRollNumberUnique) {
          throw new ServiceError(
            ErrorCode.ROLL_NUMBER_TAKEN,
            'Roll number already exists in this class and section',
            null,
            'Update student'
          );
        }
      }

      const student = await super.update(id, data);

      this.log('info', 'Student updated successfully', { studentId: id });
      return student;
    } catch (error) {
      this.log('error', 'Error updating student', { error, studentId: id });
      throw ErrorHandler.handle(error, 'Update student');
    }
  }
}
