// src/app/student-management/_components/student-management.tsx
'use client';

import { useState } from 'react';
import AddStudentWizard from './add-student-wizard';
import EnhancedEnrollmentWizard from './enhanced-enrollment-wizard';
import StudentList from './student-list';

interface StudentManagementProps {
  activeSubSection?: string;
  onSubSectionChange?: (section: string) => void;
}

const StudentManagement = ({ activeSubSection = 'Current Students' }: StudentManagementProps) => {
  const [showAddStudentWizard, setShowAddStudentWizard] = useState(false);
  const [showEnhancedWizard, setShowEnhancedWizard] = useState(false);

  const handleAddNewStudent = () => {
    setShowAddStudentWizard(true);
  };

  const handleStartEnhancedEnrollment = () => {
    setShowEnhancedWizard(true);
  };

  const handleCloseWizard = () => {
    setShowAddStudentWizard(false);
  };

  const handleCloseEnhancedWizard = () => {
    setShowEnhancedWizard(false);
  };

  const handleStudentSuccess = (message: string) => {
    console.log('Student saved successfully:', message);
    setShowAddStudentWizard(false);
    // You could show a success toast here
  };

  const handleStudentError = (_error: string) => {
    // You could show an error toast here
  };

  const handleEnrollmentComplete = (result: any) => {
    console.log('Enrollment completed successfully:', result);
    setShowEnhancedWizard(false);
    // You could show a success toast here
  };

  const handleEnrollmentCancel = () => {
    setShowEnhancedWizard(false);
  };

  const renderContent = () => {
    switch (activeSubSection) {
      case 'Current Students':
        return <StudentList onAddNewStudent={handleAddNewStudent} />;

      case 'Enroll Student':
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Enroll New Student</h3>
              <p className="text-sm text-gray-600 mb-6">Start the enrollment process for a new student using our enhanced enrollment system.</p>

              <div className="space-y-4">
                <button
                  onClick={handleStartEnhancedEnrollment}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg text-sm font-medium transition-colors shadow-sm flex items-center justify-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  <span>Start Enhanced Enrollment</span>
                </button>

                <p className="text-sm text-gray-500 text-center">or</p>

                <button
                  onClick={handleAddNewStudent}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium underline"
                >
                  Use Legacy Enrollment Form
                </button>
              </div>
            </div>
          </div>
        );

      case 'Class Assignment':
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Class Assignment Management</h3>
              <p className="text-sm text-gray-600">This feature will allow you to assign students to classes and sections, manage class capacity, and handle transfers between classes.</p>
            </div>
          </div>
        );

      case 'School Records':        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">School Records Management</h3>
              <p className="text-sm text-gray-600">This feature will provide access to student transcripts, report cards, attendance records, and other important academic documents.</p>
            </div>
          </div>
        );

      default:
        return <StudentList onAddNewStudent={handleAddNewStudent} />;
    }
  };

  return (
    <>
      {/* Hero Section - Modern and Professional (25vh) */}
      <div className="relative overflow-hidden bg-gradient-to-br from-emerald-100 via-cyan-50 to-sky-100 rounded-lg p-6 shadow-lg border border-white/50 h-[25vh] min-h-[200px] mb-8">
        {/* Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-emerald-200/20 via-transparent to-sky-200/20"></div>
        <div className="absolute top-0 right-0 w-28 h-28 bg-gradient-to-bl from-cyan-200/30 to-transparent rounded-full blur-xl"></div>
        <div className="absolute bottom-0 left-0 w-28 h-28 bg-gradient-to-tr from-emerald-200/30 to-transparent rounded-full blur-xl"></div>
        
        <div className="relative z-10 h-full flex flex-col justify-between">
          <div className="flex items-start justify-between">
            <div className="max-w-3xl">
              <div className="inline-flex items-center px-3 py-1 bg-white/70 backdrop-blur-sm rounded-full border border-emerald-200/50 mb-3">
                <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                <span className="text-sm font-medium text-emerald-800">Student Management Hub</span>
              </div>
              <h1 className="text-2xl font-bold mb-2 bg-gradient-to-r from-emerald-800 via-cyan-700 to-sky-800 bg-clip-text text-transparent leading-tight">
                Student Information System
              </h1>
              <p className="text-sm text-slate-700 leading-relaxed max-w-2xl">
                Comprehensive student management platform for enrollment, academic records, and administrative tasks. 
                Streamline your student operations with powerful tools and insights.
              </p>
            </div>
          </div>

          {/* Stats Overlay - Inline Display */}
          <div className="grid grid-cols-4 gap-8 mt-4">
            {/* Total Students */}
            <div className="flex items-center gap-3 opacity-90 hover:opacity-100 transition-all duration-300 cursor-pointer group">
              <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border border-white/30">
                <svg className="w-6 h-6 text-emerald-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <div>
                <p className="text-lg font-bold text-emerald-800 leading-tight drop-shadow-sm">1,247</p>
                <p className="text-xs font-medium text-emerald-700 leading-normal drop-shadow-sm">Total Students</p>
              </div>
            </div>

            {/* Active Enrollments */}
            <div className="flex items-center gap-3 opacity-90 hover:opacity-100 transition-all duration-300 cursor-pointer group">
              <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border border-white/30">
                <svg className="w-6 h-6 text-cyan-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <div>
                <p className="text-lg font-bold text-cyan-800 leading-tight drop-shadow-sm">89</p>
                <p className="text-xs font-medium text-cyan-700 leading-normal drop-shadow-sm">Active Enrollments</p>
              </div>
            </div>

            {/* Pending Applications */}
            <div className="flex items-center gap-3 opacity-90 hover:opacity-100 transition-all duration-300 cursor-pointer group">
              <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border border-white/30">
                <svg className="w-6 h-6 text-sky-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <p className="text-lg font-bold text-sky-800 leading-tight drop-shadow-sm">23</p>
                <p className="text-xs font-medium text-sky-700 leading-normal drop-shadow-sm">Pending Applications</p>
              </div>
            </div>

            {/* Classes Active */}
            <div className="flex items-center gap-3 opacity-90 hover:opacity-100 transition-all duration-300 cursor-pointer group">
              <div className="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 border border-white/30">
                <svg className="w-6 h-6 text-blue-700" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth="2">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <div>
                <p className="text-lg font-bold text-blue-800 leading-tight drop-shadow-sm">42</p>
                <p className="text-xs font-medium text-blue-700 leading-normal drop-shadow-sm">Active Classes</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {renderContent()}

      {/* Add Student Wizard Modal */}
      {showAddStudentWizard && (
        <AddStudentWizard
          onClose={handleCloseWizard}
          onSuccess={handleStudentSuccess}
          onError={handleStudentError}
        />
      )}

      {/* Enhanced Enrollment Wizard Modal */}
      {showEnhancedWizard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Enhanced Student Enrollment</h2>
                <button
                  onClick={handleCloseEnhancedWizard}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <EnhancedEnrollmentWizard
                onComplete={handleEnrollmentComplete}
                onCancel={handleEnrollmentCancel}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default StudentManagement;
