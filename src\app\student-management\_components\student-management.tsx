// src/app/student-management/_components/student-management.tsx
'use client';

import { useState } from 'react';
import AddStudentWizard from './add-student-wizard';
import EnhancedEnrollmentWizard from './enhanced-enrollment-wizard';
import StudentList from './student-list';

interface StudentManagementProps {
  activeSubSection?: string;
  onSubSectionChange?: (section: string) => void;
}

const StudentManagement = ({ activeSubSection = 'Current Students' }: StudentManagementProps) => {
  const [showAddStudentWizard, setShowAddStudentWizard] = useState(false);
  const [showEnhancedWizard, setShowEnhancedWizard] = useState(false);

  const handleAddNewStudent = () => {
    setShowAddStudentWizard(true);
  };

  const handleStartEnhancedEnrollment = () => {
    setShowEnhancedWizard(true);
  };

  const handleCloseWizard = () => {
    setShowAddStudentWizard(false);
  };

  const handleCloseEnhancedWizard = () => {
    setShowEnhancedWizard(false);
  };

  const handleStudentSuccess = (message: string) => {
    console.log('Student saved successfully:', message);
    setShowAddStudentWizard(false);
    // You could show a success toast here
  };

  const handleStudentError = (_error: string) => {
    // You could show an error toast here
  };

  const handleEnrollmentComplete = (result: any) => {
    console.log('Enrollment completed successfully:', result);
    setShowEnhancedWizard(false);
    // You could show a success toast here
  };

  const handleEnrollmentCancel = () => {
    setShowEnhancedWizard(false);
  };

  const renderContent = () => {
    switch (activeSubSection) {
      case 'Current Students':
        return <StudentList onAddNewStudent={handleAddNewStudent} />;

      case 'Enroll Student':
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Enroll New Student</h3>
              <p className="text-sm text-gray-600 mb-6">Start the enrollment process for a new student using our enhanced enrollment system.</p>

              <div className="space-y-4">
                <button
                  onClick={handleStartEnhancedEnrollment}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-6 rounded-lg text-sm font-medium transition-colors shadow-sm flex items-center justify-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  <span>Start Enhanced Enrollment</span>
                </button>

                <p className="text-sm text-gray-500 text-center">or</p>

                <button
                  onClick={handleAddNewStudent}
                  className="text-blue-600 hover:text-blue-700 text-sm font-medium underline"
                >
                  Use Legacy Enrollment Form
                </button>
              </div>
            </div>
          </div>
        );

      case 'Class Assignment':
        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Class Assignment Management</h3>
              <p className="text-sm text-gray-600">This feature will allow you to assign students to classes and sections, manage class capacity, and handle transfers between classes.</p>
            </div>
          </div>
        );

      case 'School Records':        return (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">School Records Management</h3>
              <p className="text-sm text-gray-600">This feature will provide access to student transcripts, report cards, attendance records, and other important academic documents.</p>
            </div>
          </div>
        );

      default:
        return <StudentList onAddNewStudent={handleAddNewStudent} />;
    }
  };

  return (
    <>
      {renderContent()}

      {/* Add Student Wizard Modal */}
      {showAddStudentWizard && (
        <AddStudentWizard
          onClose={handleCloseWizard}
          onSuccess={handleStudentSuccess}
          onError={handleStudentError}
        />
      )}

      {/* Enhanced Enrollment Wizard Modal */}
      {showEnhancedWizard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Enhanced Student Enrollment</h2>
                <button
                  onClick={handleCloseEnhancedWizard}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <EnhancedEnrollmentWizard
                onComplete={handleEnrollmentComplete}
                onCancel={handleEnrollmentCancel}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default StudentManagement;
