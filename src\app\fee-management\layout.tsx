// src/app/fee-management/layout.tsx
import { Metadata } from 'next';
import AppLayout from '@/components/layout/app-layout';

export const metadata: Metadata = {
  title: 'Fee Management - EduPro',
  description: 'Manage student fees, payments, and financial records',
};

interface FeeManagementLayoutProps {
  children: React.ReactNode;
}

export default function FeeManagementLayout({ children }: FeeManagementLayoutProps) {
  return (
    <AppLayout>
      {children}
    </AppLayout>
  );
}
