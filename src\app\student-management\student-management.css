/*
===============================================================================
                    STUDENT MANAGEMENT PAGE STYLES
===============================================================================

This file contains styles specific to the Student Management section.
It inherits from the global design system and provides consistent styling
for student management components, lists, and interfaces.

DESIGN PRINCIPLES:
- Inherit typography from globals.css design system
- Consistent component styling across student management features
- Professional appearance with proper spacing
- Responsive design for mobile and desktop

===============================================================================
*/

/* Import global design system */
@import '../../globals.css';

/* Student Management Container */
.student-management {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Page Header Styles */
.student-management-header {
  @apply mb-8;
}

.student-management-title {
  @apply text-3xl-app font-bold text-gray-900 mb-2;
}

.student-management-subtitle {
  @apply text-base-app text-gray-600;
}

/* Action Cards Styles */
.student-action-cards {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8;
}

.student-action-card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-all duration-200;
}

.student-action-card-icon {
  @apply w-12 h-12 rounded-xl flex items-center justify-center mb-4;
}

.student-action-card-title {
  @apply text-lg-app font-semibold text-gray-900 mb-2;
}

.student-action-card-description {
  @apply text-base-app text-gray-600 mb-6;
}

.student-action-card-button {
  @apply btn-primary flex items-center space-x-2 mx-auto;
}

/* Student List Styles */
.student-list-container {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden;
}

.student-list-header {
  @apply bg-gray-50 px-6 py-4 border-b border-gray-200;
}

.student-list-title {
  @apply text-lg-app font-semibold text-gray-900;
}

.student-list-filters {
  @apply flex flex-wrap items-center gap-4 mt-4;
}

.student-filter-input {
  @apply form-input-md flex-1 min-w-64;
}

.student-filter-select {
  @apply form-select-md min-w-40;
}

/* Student Table Styles */
.student-table {
  @apply w-full;
}

.student-table-header {
  @apply bg-gray-800 text-gray-200;
}

.student-table-header-cell {
  @apply px-6 py-3 text-left text-sm-app font-medium uppercase tracking-wide;
}

.student-table-row {
  @apply hover:bg-gray-50 transition-colors duration-150 border-b border-gray-200 last:border-b-0;
}

.student-table-cell {
  @apply px-6 py-4 text-base-app text-gray-900;
}

.student-table-cell-secondary {
  @apply px-6 py-4 text-sm-app text-gray-600;
}

/* Student Card Styles (for mobile view) */
.student-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-4;
}

.student-card-header {
  @apply flex items-center justify-between mb-3;
}

.student-card-name {
  @apply text-base-app font-semibold text-gray-900;
}

.student-card-id {
  @apply text-sm-app text-gray-500;
}

.student-card-details {
  @apply space-y-2;
}

.student-card-detail {
  @apply flex justify-between items-center;
}

.student-card-label {
  @apply text-sm-app text-gray-600;
}

.student-card-value {
  @apply text-sm-app text-gray-900 font-medium;
}

/* Status Badges */
.student-status-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs-app font-medium;
}

.student-status-active {
  @apply bg-green-100 text-green-800;
}

.student-status-inactive {
  @apply bg-red-100 text-red-800;
}

.student-status-pending {
  @apply bg-yellow-100 text-yellow-800;
}

/* Action Buttons */
.student-action-buttons {
  @apply flex items-center space-x-2;
}

.student-action-btn {
  @apply p-2 rounded-lg transition-colors duration-200;
}

.student-action-btn-view {
  @apply text-blue-600 hover:bg-blue-50;
}

.student-action-btn-edit {
  @apply text-green-600 hover:bg-green-50;
}

.student-action-btn-delete {
  @apply text-red-600 hover:bg-red-50;
}

/* Empty State */
.student-empty-state {
  @apply text-center py-12;
}

.student-empty-state-icon {
  @apply w-16 h-16 mx-auto mb-4 text-gray-400;
}

.student-empty-state-title {
  @apply text-lg-app font-semibold text-gray-900 mb-2;
}

.student-empty-state-description {
  @apply text-base-app text-gray-600 mb-6;
}

.student-empty-state-button {
  @apply btn-primary;
}

/* Loading States */
.student-loading {
  @apply flex items-center justify-center py-12;
}

.student-loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600;
}

.student-loading-text {
  @apply ml-3 text-base-app text-gray-600;
}

/* Pagination */
.student-pagination {
  @apply flex items-center justify-between px-6 py-4 bg-gray-50 border-t border-gray-200;
}

.student-pagination-info {
  @apply text-sm-app text-gray-700;
}

.student-pagination-buttons {
  @apply flex items-center space-x-2;
}

.student-pagination-btn {
  @apply btn-sm btn-outline;
}

.student-pagination-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* Search and Filter Section */
.student-search-section {
  @apply mb-6;
}

.student-search-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
}

.student-search-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4;
}

.student-search-input {
  @apply form-input-md;
}

.student-search-select {
  @apply form-select-md;
}

.student-search-buttons {
  @apply flex items-center space-x-2 mt-4;
}

.student-search-btn {
  @apply btn-primary;
}

.student-clear-btn {
  @apply btn-secondary;
}

/* Responsive Design */
@media (max-width: 768px) {
  .student-action-cards {
    @apply grid-cols-1 gap-4;
  }
  
  .student-action-card {
    @apply p-4;
  }
  
  .student-action-card-title {
    @apply text-base-app;
  }
  
  .student-action-card-description {
    @apply text-sm-app;
  }
  
  .student-list-filters {
    @apply flex-col items-stretch gap-3;
  }
  
  .student-filter-input,
  .student-filter-select {
    @apply w-full min-w-0;
  }
  
  .student-search-grid {
    @apply grid-cols-1 gap-3;
  }
  
  .student-pagination {
    @apply flex-col space-y-3 items-stretch;
  }
  
  .student-pagination-buttons {
    @apply justify-center;
  }
}

/* Print Styles */
@media print {
  .student-action-cards,
  .student-search-section,
  .student-pagination {
    @apply hidden;
  }
  
  .student-list-container {
    @apply shadow-none border-0;
  }
  
  .student-table-row:hover {
    @apply bg-transparent;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .student-action-card {
    @apply border-2 border-gray-400;
  }
  
  .student-list-container {
    @apply border-2 border-gray-400;
  }
  
  .student-table-header {
    @apply bg-black text-white;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .student-action-card,
  .student-table-row,
  .student-action-btn {
    @apply transition-none;
  }
  
  .student-loading-spinner {
    @apply animate-none;
  }
}
